name: 🚀 Feature
description: "Submit a proposal for a new feature"
title: "Feature: "
labels: ["enhancement", "needs-triage"]
assignees: ["prakarsh-dt","nishant-d","abhibhaw"]
projects: ["Devtron Features Prioritization"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out our feature request form 🙏
  - type: textarea
    id: feature-description
    validations:
      required: true
    attributes:
      label: "🔖 Feature description"
      description: "A clear and concise description of what the feature is."
      placeholder: "You should add ..."
  - type: textarea
    id: pitch
    validations:
      required: true
    attributes:
      label: "🎤 Pitch / Usecases"
      description: "Please explain why this feature should be implemented and how it would be used. Add examples, if applicable."
      placeholder: "In my use-case, ..."
  - type: textarea
    id: alternative
    validations:
      required: false
    attributes:
      label: "🔄️ Alternative"
      description: "A clear and concise description of any alternative solutions or features you've considered."
      placeholder: "I tried, ..."
  - type: checkboxes
    id: no-duplicate-issues
    attributes:
      label: "👀 Have you spent some time to check if this issue has been raised before?"
      description: "Have you researched on internet for a similar issue or checked our older issues for a similar bug?"
      options:
        - label: "I checked and didn't find similar issue"
          required: true
  - type: checkboxes
    id: read-code-of-conduct
    attributes:
      label: "🏢 Have you read the Code of Conduct?"
      options:
        - label: "I have read the [Code of Conduct](https://github.com/devtron-labs/devtron/blob/main/CODE_OF_CONDUCT.md)"
          required: true

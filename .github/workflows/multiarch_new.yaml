name: Multi-Arch Image Check

on:
  pull_request:
    types: [opened, edited, synchronize, reopened]


jobs:
  check-multiarch:
    name: Check Multi-Arch Images
    if: github.event.pull_request.base.ref == 'main' && github.event.pull_request.head.ref == 'release-bot'
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Install yq
      run: |
        sudo apt-get update && sudo apt-get install -y jq
        sudo apt-get install -y python3-pip
        sudo pip3 install yq

    - name: Check Multi-Arch Images
      env:
         GITHUB_TOKEN: ${{ github.token }}
         PRNUM: ${{ github.event.pull_request.number }}
         REPO: ${{ github.repository }}
      run: |
         yq '.' charts/devtron/values.yaml > values.json

         #fetching container registry
         container_registry=$(yq -r '.global.containerRegistry' values.json)
         echo "Container Registry: $container_registry"

         #function to check the architecture of image
         get_image_arch() {
             local image="$1"
             local image_ref=$(echo $image | sed 's/:.*//')
             
             # skipping the check for 'inception', 'postgres' , 'postgres_exporter', 'workflow-controller', 'casbin' and 'scoop' images
             if [[ "$image_ref" == "inception" || "$image_ref" == "postgres" || "$image_ref" == "postgres_exporter" || "$image_ref" == "workflow-controller" || "$image_ref" == "casbin" || "$image_ref" == "scoop" ]]; then
                 return
             fi
            
             local arch=$(docker manifest inspect "$container_registry/$image" | jq -r '.manifests[].platform.architecture' | sort | uniq)

             # printing the error, if found any
             if [ $? -ne 0 ]; then
                 error_images+=("$image")
                 return
             fi

             if ! (echo "$arch" | grep -q "amd64" && (echo "$arch" | grep -q "arm64" || echo "$arch" | grep -q "arm")); then
                 non_multiarch_images+=("$image")
             else
                 echo "$image supports multi-architecture: $arch"
             fi
         }

         #fetching the image information
         while read -r line; do
             image=$(echo "$line" | cut -d'"' -f4)
             if [ -n "$image" ]; then
                 echo "$image"
                 get_image_arch "$image"
             fi
         done < <(grep -Eo '"image":\s*"[^"]*"' values.json)

         while read -r line; do
             cicd_image=$(echo "$line" | cut -d'"' -f4)
             if [ -n "$cicd_image" ]; then
                 echo "$cicd_image"
                 get_image_arch "$cicd_image"
             fi
         done < <(grep -Eo '"cicdImage":\s*"[^"]*"' values.json)
         
          
         if [ ${#non_multiarch_images[@]} -ne 0 ]; then
             echo "The following images do not support multi-architecture:"
             printf '%s\n' "${non_multiarch_images[@]}"
             gh pr edit $PRNUM --add-label "PR:MultiArch-failed"
             gh pr edit $PRNUM --remove-label "PR:Ready-to-Review"
             exit 1
         else
             echo "All images support multi-architecture."
             echo "PR:Ready-to-Review, exiting gracefully"
             gh pr edit $PRNUM --add-label "PR:Ready-to-Review"
             gh pr edit $PRNUM --remove-label "PR:MultiArch-failed"
             exit 0
         fi

## v0.6.21

## Bugs
- fix: deployment timelines fix for helm apps (#3794)
- fix: duplicate role-group-fix (#3774)
- fix: prevented chart updation when its in use (#3755)
- fix: Unable to delete chart group with existing helm chart (#3795)
- fix: fix for deleting all pre-post cd at once didn't delete them  (#3786)
## Enhancements
- feat: removed additionalBackends from app-values.yaml (#3807)
- feat: added support for extra backendPath in ingress  (#3793)
- perf: hibernate check optimisation (#3788)
## Documentation
- doc: Update devtron-reference charts with pdb (#3719)
- doc: include the purpose of admin login in Devtron installation doc + FAQ (#3790)
- doc: manual image approval (#3649)
- doc: mandatory tags feature (#3630)
- doc: ci-trigger documentation update (#3629)
- docs: container lifecycle  (#3623)
- doc: security feature doc (#3622)
- doc: HashiCorp external secret operator (#3608)
## Others
- chore: adding logs in chart repo code (#3810)
- chore: Config approval scripts and refactoring (#3762)



## v0.6.27

## Bugs
- fix: implemented cache for storing cloud provider  (#4591)
- fix: panic handling when giving empty values.yaml in custom packaged chart (#4586)
- fix: 500 fixes second iteration (#4464)
- fix: sql script added for putting Identifier type=0 for all null values present in generic_note (#4568)
- fix: Image-Scanner status for failed request (#4513)
- fix: CD stage trigger is not working for external CI (#4440)
- fix: resource tree err handling (#4530)
- fix: error handling for trigger release (#4488)
- fix: show deployed time for artifacts those were deployed in past (#4446)
- fix: adding new fields in resource node to identify hooks in case of helm deployment (#4472)
- fix: panic in app clone service for [linked ci, external ci, linked cd] cases (#4526)
- fix: Error code changed for resource api (#4414)
- fix: handle argo delete event for charts and added socket config (#4471)
- fix: app clone breaking if ci pipeline have same name (#4461)
- fix: empty pod name (#4454)
- fix: Manual sync job fix (#4449)
- fix: linked ci failing for null pipeline_type (#4441)
- fix: error handling and url fix (#4407)
- feat: skip bulk build source change (#4357)
## Enhancements
- feat: Container Image Exporter Plugin (#4556)
- feat: using image digest in deployment (#4515)
- feat: Secret viewable key enhancements in get resource api and draft api (#4537)
- feat: introduced CASBIN_DATABASE env to make casbin db configurable (#4547)
- feat: added posthog events for cloud provider and version (#4443)
- feat: making pre,post,deploy triggers flows idempotent (#4486)
- feat: Create Dockerslim plugin and Create EKS Cluster plugin (#4525)
- feat: Adds Copacetic plugin to patch vulnerable images  (#4566)
- feat: Adds support for gRPC in health probes (#4495)
- feat: Configurable namespace for secrets in dex config  (#4499)
- feat: sql script update (#4522)
- feat: refactoring authorisation checks and support of super-admin in permissions groups. (#4433)
- feat: add or delete CD pipelines from workflow (#4398)
- feat: common-lib version update (#4399)
- feat: flag driven tag based build propogate same tag for CI Build  (#4404)
## Documentation
- doc: Redirection added for old SSO doc link (#4607)
- doc: added --reuse-values in troubleshoot (#4577)
- docs: Segregated SSO Docs + Added Auto-Assign Permissions Feature (#4493)
- doc: Created Catalog Framework Doc (#4512)
- doc: Added Minikube Tutorial + Restructured Doc (#4477)
- doc: Update Helm Installation Command (#4429)
- docs: Typos fixed in multiple files of user-guide/creating-application (#4098)
- docs: fix broken links (#4186)
- doc: Update Install-devtron-on-Minikube-Microk8s-K3s-Kind.md (#4133)
- doc: fixed broken link and improved documentation in usecases (#4097)
## Others
- chore:  Enhanced SonarQube Script by exposing Docker Image (#4600)
- chore: fix for the extra [ ] in volume mount in statefulset chart (#4553)
- chore: added recovery in crons recovery (#4592)
- chore: code refactoring v1 (#4545)
- chore: panic middleware and proxy handler (#4546)
- chore: added sql-validator in git-hub action (#4255)
- chore: Update Issues template and codeowners (#4475)
- chore: add back argo-assets (#4467)
- chore: dependabot version upgrade nats metrics oss (#4453)
- chore: share same nats url used by orchestrator (#4422)
- chore: migration for global resource schema (#4374)




## v0.6.26

## Bugs
- fix: chart sync duplicate charts (#4321)
- fix: optimised the artifacts listing query and added query versioned support (#4375)
## Enhancements
- feat: CS create bucket plugin (#4411)
- feat: Integrate GKE provisioning into Devtron Plugin (#4406)
- feat: Integrate Cloudanix Image Scanner Plugin for Advanced Image Scanning (#4396)
- feat: Support for OCI charts pull & deploy in EA mode (#4309)
## Documentation
- docs: Typos fixed in multiple docs (Phase 1) (#4402)
- doc: Redirection added for OCI doc (#4394)
- doc: Indentation fix in Scoped Variable Doc (#4367)
- docs: Gitbook assets moved to AWS S3 (#4380)
## Others
- chore: Updated migration number for gcs create bucket (#4412)
- chore:  enhancements in User Service and User common service (#4293)
- chore: Remove the annotation hook from devtron-nats-test-request-reply pod (#4387)
- chore: Update pager-duty Issue template (#4381)
- chore: made devtron namespace configurable in authenticator (#4271)
- chore: added migration script for dependency (#4279)



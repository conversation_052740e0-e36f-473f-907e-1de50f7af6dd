## v0.6.23



## Bugs
- fix: DT19-v1 bug fixes (#3962)
- fix: ci pod request correction (#3980)
- fix: pipelineOverride id being sent instead of pipelineId (#3984)
- fix: Iam role handling script for plugin pull image from CR (#3955)
- fix: Deployment Template HCL parsing with % keyword (#4012)
- fix: handled releaseNotExists case for helm type cd pipeline resource tree fetch (#4016)
- fix: auto post cd not working in case of multiple parallel gitOps pipeline (#4018)
- fix: handled error in bulk trigger deploy (#4034)
- fix: The manager(non-admin user) of the application is unable to select a list of apps when assigning permissions (#4053)
- fix: ci job handling in app create api (#4054)
- fix: Deploying currently Active image using TriggerDeploy API from devtctl tool is broken (#4056)
- fix: Unable to delete ci pipeline in case you configure multi git (#4072)
- fix: env for specific deployment (#4085)
- fix: update build configuration fix (#4093)
- fix: Artifacts filter in CD trigger view (#4064)
- fix: Bugathon DT-19 version-2 fixes (#4105)
- fix: App Labels node selector not getting attach in ci-workflow (#4084)
- fix: Update cd pipeline create empty pre post cd steps (#4113)
- fix: normal Refresh after triggering gitops deployment to avoid sync delay in argo (#4066)
- fix: helm chart delete when no rows are found (#4124)
- fix: Unable to abort pre-cd and post-cd workflow (#4121)
- fix: Helm Apps permissions do not allow Terminal or Logs view (#4110)
- fix: port service mapping (#4132)
## Enhancements
- feat: Helm async install (#3856)
- feat: handle CI success event auto trigger in batch (#3951)
- feat: added env variable to skip gitops validation on create/update (#3956)
- feat: added flag to configure ecr repo creation (#3963)
- feat: Ability to change branch for all selected applications during bulk build from Application Groups (#3955)
- feat: Variables support in pre-post CI, CD and Jobs (#3911)
- feat: Poll Images from ECR Container Repository Plugin (#3971)
- feat: resource groups CRUD and environment filtering (#3974)
- feat: Scoped variables primitive handling (#4033)
- feat: adding DEVTRON_APP_NAME system variable for deployment template (#4041)
- feat: wf pod restart (#3892)
- feat: added deduction for system variables (#4075)
- feat: manifest comparision (#3844)
- feat: multiple images handling for single workflow for ECR Plugin Poll Images (#4027)
- feat: Jenkins plugin migration (#4039)
- feat: clone cd pipelines while cloning app across project (#4087)
## Documentation
- doc: Glossary of jargonish terms for layman in the context of Devtron (#3820)
- docs: Ephemeral Container Doc (#3912)
- docs: New Image Alignment in Ephemeral doc (#3959)
- docs: Snapshot updation in PVC docs + PreBuild CI-CD (#3964)
- doc: Fixed issuer url in okta docs (#4062)
- docs: Config Approval Draft (#3981)
- docs: Modified Existing Container Registry Doc (#4048)
- docs: Added OCI Pull in Usecases (#4112)
## Others
- chore: added workflow to escalate pager-duty issue (#3927)
- chore: changed loop from for to while (#3928)
- chore: scheduled escalate pager duty issue workflow (#3933)
- chore: added log config for dev mode (#3953)
- chore: minor correction in devtron reference charts (#3957)
- chore: workflow refactoring (#3714)
- chore: pr-issue-validator permissions fix (#3967)
- chore: added CODEOWNERS (#3966)
- chore: Scoped variable refactoring (#3977)
- chore: modified labels of keda autoscale object in deployment chart (#3999)
- chore: Update pr-issue-validator.yaml (#3854)
- chore: refactoring around PipelineBuilder (#4043)
- chore: moved k8s library to common-lib and added scripts for adding sshTunnel config to clusters (#3848)
- chore: Add pager-duty issue template (#3988)
- chore: first cut refactor ci-pipeline (#4091)
- chore: refactored appartifact manager and cimaterialconfigservice (#4096)
- chore: Remove the EnvVariablesFromFieldPath from values.yaml in refcharts (#4111)
- chore: Updated schema for Scope Variable (#4079)
- chore: skip validation for release PRs (#4128)

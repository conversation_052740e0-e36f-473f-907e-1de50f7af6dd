apiVersion: v1
kind: ConfigMap
metadata:
  name: external-secret-override-cm
  namespace:  devtroncd
data:
  override: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: devtron-kubernetes-external-secrets
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                limits:
                  cpu: 0.01
                  memory: 100Mi
                requests:
                  cpu: 0.01
                  memory: 100Mi
    #              env:
    #              - name: "LOG_LEVEL"
    #                value: "info"
    #              - name: "LOG_MESSAGE_KEY"
    #                value: "msg"
    #              - name: "METRICS_PORT"
    #                value: "3001"
    #              - name: "POLLER_INTERVAL_MILLISECONDS"
    #                value: "10000"
{"__inputs": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.0.3"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": 13321, "graphTooltip": 0, "id": null, "iteration": 1604308066386, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code=~\"$response_code\", response_code_class=~\"$response_code_class\", appId=\"$app\", envId=\"$env\", job!~\".*preview.*\"}[1m])*60, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{rollouts_pod_template_hash}}", "refId": "A"}, {"expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code=~\"$response_code\", response_code_class=~\"$response_code_class\", app_id=\"$app\", env_id=\"$env\", job!~\".*preview.*\"}[1m])*60, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{rollouts_pod_template_hash}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "rpm", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code=~\"$response_code\", response_code_class=~\"$response_code_class\", appId=\"$app\", envId=\"$env\", job!~\".*preview.*\"}[1m])*60, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}, {"expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code=~\"$response_code\", response_code_class=~\"$response_code_class\", app_id=\"$app\", env_id=\"$env\", job!~\".*preview.*\"}[1m])*60, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "rpm", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by(pod,app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code_class=~\"$response_code_class\",response_code=~\"$response_code\", appId=\"$app\", envId=\"$env\", job!~\".*preview.*\"}[1m])*60, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}, {"expr": "sum by(pod,app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code_class=~\"$response_code_class\",response_code=~\"$response_code\", app_id=\"$app\", env_id=\"$env\", job!~\".*preview.*\"}[1m])*60, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "rpm", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by(pod,app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code_class=~\"$response_code_class\",response_code=~\"$response_code\", appId=\"$app\", envId=\"$env\", job!~\".*preview.*\"}[1m])*60, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}, {"expr": "sum by(pod,app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code_class=~\"$response_code_class\",response_code=~\"$response_code\", app_id=\"$app\", env_id=\"$env\", job!~\".*preview.*\"}[1m])*60, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "rpm", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 25, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "$datasource", "definition": "label_values(kube_pod_labels,label_app_id)", "hide": 0, "includeAll": false, "label": "app", "multi": false, "name": "app", "options": [], "query": "label_values(kube_pod_labels,label_app_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "$datasource", "definition": "label_values(kube_pod_labels,label_envId)", "hide": 0, "includeAll": false, "label": "env", "multi": false, "name": "env", "options": [], "query": "label_values(kube_pod_labels,label_envId)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "84c89c85b7", "value": "84c89c85b7"}, "hide": 0, "label": "new_rollout_pod_template_hash", "name": "new_rollout_pod_template_hash", "options": [{"selected": false, "text": "65d74b6698", "value": "65d74b6698"}], "query": "84c89c85b7", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "Prometheus-devtron", "value": "Prometheus-devtron"}, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {}, "datasource": "$datasource", "definition": "label_values(response_code_class, response_code_class)", "hide": 0, "includeAll": false, "label": "response_code_class", "multi": false, "name": "response_code_class", "options": [], "query": "label_values(response_code_class, response_code_class)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "$datasource", "definition": "label_values(response_code, response_code)", "hide": 0, "includeAll": false, "label": "response_code", "multi": false, "name": "response_code", "options": [], "query": "label_values(response_code, response_code)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "res_status_per_pod", "uid": "NnFpQOKGk", "version": 1, "description": "Graph shows http status like 4xx, 5xx, 200, Throughput on Devtron dashboard.\n\nhttps://www.devtron.ai"}
{"__inputs": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.0.3"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": 13323, "graphTooltip": 0, "id": null, "iteration": 1604309275109, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (label_appId,label_envId,label_rollouts_pod_template_hash) (\n    label_replace(\n    max(\n    kube_pod_labels{label_appId=\"$app\",label_envId=\"$env\"}) by (label_appId,label_envId, pod, label_rollouts_pod_template_hash)\n    ,\"label_rollouts_pod_template_hash\",  \"$1(new)\", \"label_rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\")\n    *\n    on(pod)\n    group_right(label_appId, label_envId, label_rollouts_pod_template_hash)\n    label_replace(\n       sum by(pod_name) (\n           container_memory_working_set_bytes{container!=\"POD\", container!=\"\"}\n       ), \"pod\", \"$1\",\"pod_name\", \"(.+)\"\n    )\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{label_rollouts_pod_template_hash}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 0}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (label_appId,label_envId,label_rollouts_pod_template_hash) (\n    label_replace(\n    max(\n    kube_pod_labels{label_appId=\"$app\",label_envId=\"$env\"}) by (label_appId,label_envId, pod, label_rollouts_pod_template_hash)\n    ,\"label_rollouts_pod_template_hash\",  \"$1(new)\", \"label_rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\")\n    *\n    on(pod)\n    group_right(label_appId, label_envId, label_rollouts_pod_template_hash)\n    label_replace(\n       sum by(pod_name) (\n           rate(container_memory_usage_bytes[1m])\n       ), \"pod\", \"$1\",\"pod_name\", \"(.+)\"\n    )\n)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{label_rollouts_pod_template_hash}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (pod,label_appId,label_envId,label_rollouts_pod_template_hash) (\n    label_replace(\n    max(\n    kube_pod_labels{label_appId=\"$app\",label_envId=\"$env\"}) by (label_appId,label_envId, pod, label_rollouts_pod_template_hash)\n    ,\"label_rollouts_pod_template_hash\",  \"$1(new)\", \"label_rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\")\n    *\n    on(pod)\n    group_right(label_appId, label_envId, label_rollouts_pod_template_hash)\n    label_replace(\n       sum by(pod_name) (\n           container_memory_usage_bytes{container!=\"POD\", container!=\"\"}\n       ), \"pod\", \"$1\",\"pod_name\", \"(.+)\"\n    )\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 11, "x": 12, "y": 10}, "hiddenSeries": false, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (pod,label_appId,label_envId,label_rollouts_pod_template_hash) (\n    label_replace(\n    max(\n    kube_pod_labels{label_appId=\"$app\",label_envId=\"$env\"}) by (label_appId,label_envId, pod, label_rollouts_pod_template_hash)\n    ,\"label_rollouts_pod_template_hash\",  \"$1(new)\", \"label_rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\")\n    *\n    on(pod)\n    group_right(label_appId, label_envId, label_rollouts_pod_template_hash)\n    label_replace(\n       sum by(pod_name) (\n           rate(container_memory_usage_bytes[1m])\n       ), \"pod\", \"$1\",\"pod_name\", \"(.+)\"\n    )\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 25, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "$datasource", "definition": "label_values(kube_pod_labels,label_appId)", "hide": 0, "includeAll": false, "label": "app", "multi": false, "name": "app", "options": [], "query": "label_values(kube_pod_labels,label_appId)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "$datasource", "definition": "label_values(kube_pod_labels,label_envId)", "hide": 0, "includeAll": false, "label": "env", "multi": false, "name": "env", "options": [], "query": "label_values(kube_pod_labels,label_envId)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "65d74b6698", "value": "65d74b6698"}, "hide": 0, "label": "new_rollout_pod_template_hash", "name": "new_rollout_pod_template_hash", "options": [{"selected": true, "text": "75b58ddbd", "value": "75b58ddbd"}], "query": "75b58ddbd", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "Prometheus-client-uat", "value": "Prometheus-client-uat"}, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "kfc", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "memory-usage-k8s15", "uid": "devtron-app-metrics-memory-k8s15", "version": 3, "description": "This Graph shows pod memory usage on Devtron dashboard.\n\nhttps://www.devtron.ai"}
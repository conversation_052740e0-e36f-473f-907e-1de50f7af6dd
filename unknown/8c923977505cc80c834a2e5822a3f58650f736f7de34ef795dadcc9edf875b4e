// Code generated by mockery v2.33.1. DO NOT EDIT.

package mocks

import (
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/pkg/variables/repository"
)

// VariableEntityMappingRepository is an autogenerated mock type for the VariableEntityMappingRepository type
type VariableEntityMappingRepository struct {
	mock.Mock
}

// CommitTx provides a mock function with given fields: tx
func (_m *VariableEntityMappingRepository) CommitTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteAllVariablesForEntities provides a mock function with given fields: entities, userId
func (_m *VariableEntityMappingRepository) DeleteAllVariablesForEntities(entities []repository.Entity, userId int32) error {
	ret := _m.Called(entities, userId)

	var r0 error
	if rf, ok := ret.Get(0).(func([]repository.Entity, int32) error); ok {
		r0 = rf(entities, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteVariablesForEntity provides a mock function with given fields: tx, variableIDs, entity, userId
func (_m *VariableEntityMappingRepository) DeleteVariablesForEntity(tx *pg.Tx, variableIDs []string, entity repository.Entity, userId int32) error {
	ret := _m.Called(tx, variableIDs, entity, userId)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, []string, repository.Entity, int32) error); ok {
		r0 = rf(tx, variableIDs, entity, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetVariablesForEntities provides a mock function with given fields: entities
func (_m *VariableEntityMappingRepository) GetVariablesForEntities(entities []repository.Entity) ([]*repository.VariableEntityMapping, error) {
	ret := _m.Called(entities)

	var r0 []*repository.VariableEntityMapping
	var r1 error
	if rf, ok := ret.Get(0).(func([]repository.Entity) ([]*repository.VariableEntityMapping, error)); ok {
		return rf(entities)
	}
	if rf, ok := ret.Get(0).(func([]repository.Entity) []*repository.VariableEntityMapping); ok {
		r0 = rf(entities)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.VariableEntityMapping)
		}
	}

	if rf, ok := ret.Get(1).(func([]repository.Entity) error); ok {
		r1 = rf(entities)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RollbackTx provides a mock function with given fields: tx
func (_m *VariableEntityMappingRepository) RollbackTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveVariableEntityMappings provides a mock function with given fields: tx, mappings
func (_m *VariableEntityMappingRepository) SaveVariableEntityMappings(tx *pg.Tx, mappings []*repository.VariableEntityMapping) error {
	ret := _m.Called(tx, mappings)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, []*repository.VariableEntityMapping) error); ok {
		r0 = rf(tx, mappings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StartTx provides a mock function with given fields:
func (_m *VariableEntityMappingRepository) StartTx() (*pg.Tx, error) {
	ret := _m.Called()

	var r0 *pg.Tx
	var r1 error
	if rf, ok := ret.Get(0).(func() (*pg.Tx, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *pg.Tx); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.Tx)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewVariableEntityMappingRepository creates a new instance of VariableEntityMappingRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewVariableEntityMappingRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *VariableEntityMappingRepository {
	mock := &VariableEntityMappingRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

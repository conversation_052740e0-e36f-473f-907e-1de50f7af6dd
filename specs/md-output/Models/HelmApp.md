# HelmApp
## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**appName** | **String** | name of the helm application/helm release name | [optional] [default to null]
**appId** | **String** | unique identifier for app | [optional] [default to null]
**chartName** | **String** | name of the chart | [optional] [default to null]
**chartAvatar** | **String** | url/location of the chart icon | [optional] [default to null]
**projectId** | **Integer** | unique identifier for the project, APP with no project will have id &#x60;0&#x60; | [optional] [default to null]
**environmentDetail** | [**AppEnvironmentDetail**](.md) |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


openapi: "3.0.3"
info:
  version: 1.0.0
  title: Devtron Labs
paths:
    /orchestrator/app/template/default:
      get:
        description: Get default values of template by chartRefId
        parameters:
          - name: appId
            in: query
            required: true
            schema:
              type: integer
          - name: chartRefId
            in: query
            required: true
            schema:
              type: integer
        responses:
          "200":
            description: value
            content:
              application/json:
                schema:
                  $ref: "#/components/schemas/DefaultTemplateResponse"
components:
  schemas:
    DefaultTemplateResponse:
      properties:
        appOverride:
          type: array
          items:
            type: object
            properties:
              Key:
                type: string
              Value:
                type: string
          description: template json in form of map

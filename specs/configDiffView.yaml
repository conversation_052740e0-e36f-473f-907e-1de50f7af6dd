openapi: 3.0.0
info:
  title: Orchestrator Config Autocomplete API
  version: 1.0.0
paths:
  /orchestrator/config/autocomplete:
    get:
      summary: Retrieve autocomplete data for configuration based on the provided appId and envId. The response includes configuration definitions with names, draft states, and types.
      parameters:
        - name: appId
          in: query
          description: The application ID.
          required: true
          schema:
            type: string
        - name: envId
          in: query
          description: The environment ID.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ConfigProperty"

        '500':
          description: will get this response if any failure occurs at server side.
        '400':
          description: will get this response if invalid payload is sent in the request.
        '403':
          description: will get this response if user doesn't view access  permission for the app or env
        '404':
          description:  will get this when BaseDeployment Template is not configured

components:
  schemas:
    ConfigDataResponse:
      type: object
      properties:
        resourceConfig:
          type: array
          items:
            $ref: '#/components/schemas/ConfigProperty'

    ConfigProperty:
      type: object
      properties:
        name:
          type: string
          description: Name of the config
          example: cm-1
          nullable: true
        configState:
           $ref: '#/components/schemas/ConfigStateEnum'
        type:
           $ref: '#/components/schemas/ResourceTypeEnum'

    ConfigStateEnum:
      type: integer
      enum: [ 1, 2, 3 ]
      description: State of config (1 represents draft state , 2 represents approval pending state,3 represents published state)

    ResourceTypeEnum:
              type: string
              enum: [ "ConfigMap", "Secret", "Deployment Template" ]
              description: Describe the config type (possible values are ConfigMap, Secret, Deployment Template)


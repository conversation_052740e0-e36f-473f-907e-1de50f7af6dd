# Terminal

A pod can have one or more containers running, and a container can have no or multiple shells running in it. 
If you are not able to create a successful connection, try changing the shell, as the container may not have that shell running. 

![](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/creating-application/terminal-controls.jpg)

### Connection Status

- Connected: connection has established successfully
- Connecting: trying to establish new connection 
- Disconnecting: trying to terminate current connection
- Disconnected: connection has terminated

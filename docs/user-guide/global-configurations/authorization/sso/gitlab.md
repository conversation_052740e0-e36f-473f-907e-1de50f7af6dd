# GitLab

## Sample Configuration

![](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/global-configurations/sso-login-service/gitlab.jpg)

---

## Values You Would Require at SSO Provider

Devtron provides a sample configuration out of the box. There are some values that you need to either get from your SSO provider or give to your SSO provider.

### Values to Fetch

* clientID
* clientSecret

    ![Fetching Client ID and Secret](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/global-configurations/sso-login-service/secret/gitlab-id-secret.jpg)

### Values to Provide

* redirectURI (provided in SSO Login Services by Devtron)

    ![Copying Redirect URI from Devtron](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/global-configurations/sso-login-service/redirect/gitlab-redurl.jpg)

    ![Pasting Redirect URI](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/global-configurations/sso-login-service/redirect/gitlab-redirect-v2.jpg)

---

## Reference

* [View GitLab Documentation](https://docs.gitlab.com/ee/integration/oauth_provider.html)

* [View Dex IdP Documentation](https://dexidp.io/docs/connectors/gitlab/)


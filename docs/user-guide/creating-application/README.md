# App Configuration

**Please configure Global Configurations before moving ahead with App Configuration**

{% embed url="https://www.youtube.com/watch?v=9u-pKiWV-tM" caption="" %}

**Parts of Documentation**

[Git Repository](git-material.md)

[Build Configuration](docker-build-configuration.md)

[Base Deployment Template](deployment-template.md)

[GitOps Configuration](gitops-config.md)

[Workflow Editor](workflow/README.md)

[ConfigMaps](config-maps.md)

[Secrets](secrets.md)

[External Links](external-links.md)

[Protect Configuration](config-approval.md)

[Environment Overrides](environment-overrides.md)

[Deleting Application](../deleting-application.md)

<!-- [Application Metrics](app-metrics.md) -->


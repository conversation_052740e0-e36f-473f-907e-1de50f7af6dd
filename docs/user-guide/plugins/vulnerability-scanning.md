# Vulnerability-Scanning

## Introduction
The **Vulnerability Scanning** plugin by Devtron enables you to scan and detect vulnerabilities of your applications using <PERSON>vy/Clair. The Vulnerability Scanning plugin is recommended to be integrated into the Job Pipeline, especially when you are using external CI pipelines like Jenkins, GitLab, or GitHub Actions. Based on Vulnerability Scanning results, you can enforce security policies to either proceed with or abort the deployment process, giving you more control over your deployment process.

### Prerequisites
Before integrating the Vulnerability Scanning plugin, ensure that you have installed the `Vulnerability Scanning (<PERSON>vy/Clair)` integration from Devtron Stack Manager.

---

## Steps
1. Go to **Applications** → **Devtron Apps**.
2. Click your application.
3. Go to **App Configuration** → **Workflow Editor**.
4. Click **New Workflow** and navigate to the **CREATE JOB PIPELINE**.
5. Enter the required fields in the **Basic configuration** window.
6. Click **Task to be executed**.
7. Under 'TASKS', click the **+ Add task** button.
8. Click the **Vulnerability Scanning** plugin.
9. Enter the following [user inputs](#user-inputs) with appropriate values.
---

## User Inputs

### Task Name
Enter the name of your task

e.g., `Vulnerability Scanning for External CI `

### Description
Add a brief explanation of the task and the reason for choosing the plugin. Include information for someone else to understand the purpose of the task.

e.g., `The Vulnerability Scanning plugin is integrated for detecting vulnerabilities in applications.`

### Input Variables

| Variable                 | Format       | Description | Sample Value |
| ------------------------ | ------------ | ----------- | ------------ |
|   IMAGE_SCAN_MAX_RETRIES | STRING       | Maximum retries for image scanning. |  2            |
|   IMAGE_SCAN_RETRY_DELAY | STRING       | Delay between image scanning retries (seconds).     | 120            |

### Trigger/Skip Condition
Here you can set conditions to execute or skip the task. You can select `Set trigger conditions` for the execution of a task or `Set skip conditions` to skip the task.

### Output Variables
Vulnerability Scanning will not be generating an output variable.

Click **Update Pipeline**.




// cpu

{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 14,
  "iteration": 1571208465823,
  "links": [],
  "panels": [
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "$datasource",
      "fill": 1,
      "gridPos": {
        "h": 9,
        "w": 12,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "nullPointMode": "null",
      "percentage": false,
      "pointradius": 5,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "sum by (label_appId,label_envId,label_rollouts_pod_template_hash) (\n    label_replace(\n    max(\n    kube_pod_labels{label_appId=\"$app\",label_envId=\"$env\"}) by (label_app,label_env, pod, label_rollouts_pod_template_hash)\n    ,\"label_rollouts_pod_template_hash\",  \"$1(new)\", \"label_rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\")\n    *\n    on(pod)\n    group_right(label_appId, label_envId, label_rollouts_pod_template_hash)\n    label_replace(\n       sum by(pod_name) (\n           rate(container_cpu_usage_seconds_total[1m])\n       ), \"pod\", \"$1\",\"pod_name\", \"(.+)\"\n    )\n)",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "{{label_rollouts_pod_template_hash}}",
          "refId": "A"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "Panel Title",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    }
  ],
  "refresh": "10s",
  "schemaVersion": 16,
  "style": "dark",
  "tags": [],
  "templating": {
    "list": [
      {
        "allValue": null,
        "current": {
          "text": "34",
          "value": "34"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_appId)",
        "hide": 0,
        "includeAll": false,
        "label": "app",
        "multi": false,
        "name": "app",
        "options": [],
        "query": "label_values(kube_pod_labels,label_appId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": null,
        "current": {
          "text": "1",
          "value": "1"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_envId)",
        "hide": 0,
        "includeAll": false,
        "label": "env",
        "multi": false,
        "name": "env",
        "options": [],
        "query": "label_values(kube_pod_labels,label_envId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "current": {
          "text": "84c89c85b7",
          "value": "84c89c85b7"
        },
        "hide": 0,
        "label": "new_rollout_pod_template_hash",
        "name": "new_rollout_pod_template_hash",
        "options": [
          {
            "text": "7f996d5cfc",
            "value": "7f996d5cfc"
          }
        ],
        "query": "7f996d5cfc",
        "skipUrlSync": false,
        "type": "textbox"
      },
      {
        "current": {
          "text": "Prometheus",
          "value": "Prometheus"
        },
        "hide": 0,
        "label": "datasource",
        "name": "datasource",
        "options": [],
        "query": "prometheus",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "type": "datasource"
      }
    ]
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ],
    "time_options": [
      "5m",
      "15m",
      "1h",
      "6h",
      "12h",
      "24h",
      "2d",
      "7d",
      "30d"
    ]
  },
  "timezone": "",
  "title": "cpu-usage",
  "uid": "devtron-app-metrics-cpu",
  "version": 1
}



// latency

{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 29,
  "iteration": 1571820632681,
  "links": [],
  "panels": [
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "$datasource",
      "fill": 1,
      "gridPos": {
        "h": 9,
        "w": 12,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "links": [],
      "nullPointMode": "null",
      "percentage": false,
      "pointradius": 5,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(envoy_cluster_upstream_rq_time_sum{appId=\"$app\",envId=\"$env\"}, \"rollouts_pod_template_hash\", \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\")\n/ label_replace(envoy_cluster_upstream_rq_time_count{appId=\"$app\",envId=\"$env\"}, \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "{{rollouts_pod_template_hash}}",
          "refId": "A"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    }
  ],
  "refresh": "10s",
  "schemaVersion": 16,
  "style": "dark",
  "tags": [],
  "templating": {
    "list": [
      {
        "allValue": null,
        "current": {
          "text": "71",
          "value": "71"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_appId)",
        "hide": 0,
        "includeAll": false,
        "label": "app",
        "multi": false,
        "name": "app",
        "options": [],
        "query": "label_values(kube_pod_labels,label_appId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": null,
        "current": {
          "text": "1",
          "value": "1"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels, label_envId)",
        "hide": 0,
        "includeAll": false,
        "label": "env",
        "multi": false,
        "name": "env",
        "options": [],
        "query": "label_values(kube_pod_labels, label_envId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "current": {
          "text": "6b68bf4b89",
          "value": "6b68bf4b89"
        },
        "hide": 0,
        "label": "new_rollout_pod_template_hash",
        "name": "new_rollout_pod_template_hash",
        "options": [
          {
            "text": "6b68bf4b89",
            "value": "6b68bf4b89"
          }
        ],
        "query": "6b68bf4b89",
        "skipUrlSync": false,
        "type": "textbox"
      },
      {
        "current": {
          "text": "Prometheus",
          "value": "Prometheus"
        },
        "hide": 0,
        "label": "datasource",
        "name": "datasource",
        "options": [],
        "query": "prometheus",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "type": "datasource"
      }
    ]
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ],
    "time_options": [
      "5m",
      "15m",
      "1h",
      "6h",
      "12h",
      "24h",
      "2d",
      "7d",
      "30d"
    ]
  },
  "timezone": "",
  "title": "latency",
  "uid": "devtron-app-metrics-latency",
  "version": 2
}


// memory-usage

{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 13,
  "iteration": 1571208470802,
  "links": [],
  "panels": [
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "$datasource",
      "fill": 1,
      "gridPos": {
        "h": 10,
        "w": 15,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "links": [],
      "nullPointMode": "null",
      "percentage": false,
      "pointradius": 5,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "sum by (label_appId,label_envId,label_rollouts_pod_template_hash) (\n    label_replace(\n    max(\n    kube_pod_labels{label_appId=\"$app\",label_envId=\"$env\"}) by (label_appId,label_envId, pod, label_rollouts_pod_template_hash)\n    ,\"label_rollouts_pod_template_hash\",  \"$1(new)\", \"label_rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\")\n    *\n    on(pod)\n    group_right(label_appId, label_envId, label_rollouts_pod_template_hash)\n    label_replace(\n       sum by(pod_name) (\n           rate(container_memory_usage_bytes[1m])\n       ), \"pod\", \"$1\",\"pod_name\", \"(.+)\"\n    )\n)",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "{{label_rollouts_pod_template_hash}}",
          "refId": "A"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    }
  ],
  "refresh": "10s",
  "schemaVersion": 16,
  "style": "dark",
  "tags": [],
  "templating": {
    "list": [
      {
        "allValue": null,
        "current": {
          "text": "34",
          "value": "34"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_appId)",
        "hide": 0,
        "includeAll": false,
        "label": "app",
        "multi": false,
        "name": "app",
        "options": [],
        "query": "label_values(kube_pod_labels,label_appId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": null,
        "current": {
          "text": "1",
          "value": "1"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_envId)",
        "hide": 0,
        "includeAll": false,
        "label": "env",
        "multi": false,
        "name": "env",
        "options": [],
        "query": "label_values(kube_pod_labels,label_envId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "current": {
          "text": "84c89c85b7",
          "value": "84c89c85b7"
        },
        "hide": 0,
        "label": "new_rollout_pod_template_hash",
        "name": "new_rollout_pod_template_hash",
        "options": [
          {
            "text": "65d74b6698",
            "value": "65d74b6698"
          }
        ],
        "query": "65d74b6698",
        "skipUrlSync": false,
        "type": "textbox"
      },
      {
        "current": {
          "text": "Prometheus",
          "value": "Prometheus"
        },
        "hide": 0,
        "label": "datasource",
        "name": "datasource",
        "options": [],
        "query": "prometheus",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "type": "datasource"
      }
    ]
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ],
    "time_options": [
      "5m",
      "15m",
      "1h",
      "6h",
      "12h",
      "24h",
      "2d",
      "7d",
      "30d"
    ]
  },
  "timezone": "",
  "title": "memory-usage",
  "uid": "devtron-app-metrics-memory",
  "version": 1
}


// 5xx codes
{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 31,
  "iteration": 1571764691425,
  "links": [],
  "panels": [
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "$datasource",
      "fill": 1,
      "gridPos": {
        "h": 9,
        "w": 12,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "links": [],
      "nullPointMode": "null",
      "percentage": false,
      "pointradius": 5,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code_class=\"5xx\", appId=\"$app\", envId=\"$env\"}[1m]), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "{{rollouts_pod_template_hash}}",
          "refId": "A"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    }
  ],
  "refresh": false,
  "schemaVersion": 16,
  "style": "dark",
  "tags": [],
  "templating": {
    "list": [
      {
        "allValue": null,
        "current": {
          "text": "71",
          "value": "71"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_appId)",
        "hide": 0,
        "includeAll": false,
        "label": "app",
        "multi": false,
        "name": "app",
        "options": [],
        "query": "label_values(kube_pod_labels,label_appId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": null,
        "current": {
          "text": "1",
          "value": "1"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_envId)",
        "hide": 0,
        "includeAll": false,
        "label": "env",
        "multi": false,
        "name": "env",
        "options": [],
        "query": "label_values(kube_pod_labels,label_envId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "current": {
          "text": "84c89c85b7",
          "value": "84c89c85b7"
        },
        "hide": 0,
        "label": "new_rollout_pod_template_hash",
        "name": "new_rollout_pod_template_hash",
        "options": [
          {
            "text": "65d74b6698",
            "value": "65d74b6698"
          }
        ],
        "query": "65d74b6698",
        "skipUrlSync": false,
        "type": "textbox"
      },
      {
        "current": {
          "text": "Prometheus",
          "value": "Prometheus"
        },
        "hide": 0,
        "label": "datasource",
        "name": "datasource",
        "options": [],
        "query": "prometheus",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "type": "datasource"
      }
    ]
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ],
    "time_options": [
      "5m",
      "15m",
      "1h",
      "6h",
      "12h",
      "24h",
      "2d",
      "7d",
      "30d"
    ]
  },
  "timezone": "",
  "title": "status_code_5xx",
  "uid": "devtron-app-metrics-5xx",
  "version": 4
}


// 2xx codes


{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 15,
  "iteration": 1571208472994,
  "links": [],
  "panels": [
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "$datasource",
      "fill": 1,
      "gridPos": {
        "h": 9,
        "w": 12,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "links": [],
      "nullPointMode": "null",
      "percentage": false,
      "pointradius": 5,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code_class=\"2xx\", appId=\"$app\", envId=\"$env\"}[1m]), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "{{rollouts_pod_template_hash}}",
          "refId": "A"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    }
  ],
  "refresh": "10s",
  "schemaVersion": 16,
  "style": "dark",
  "tags": [],
  "templating": {
    "list": [
      {
        "allValue": null,
        "current": {
          "text": "34",
          "value": "34"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_appId)",
        "hide": 0,
        "includeAll": false,
        "label": "app",
        "multi": false,
        "name": "app",
        "options": [],
        "query": "label_values(kube_pod_labels,label_appId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": null,
        "current": {
          "text": "1",
          "value": "1"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_envId)",
        "hide": 0,
        "includeAll": false,
        "label": "env",
        "multi": false,
        "name": "env",
        "options": [],
        "query": "label_values(kube_pod_labels,label_envId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "current": {
          "text": "84c89c85b7",
          "value": "84c89c85b7"
        },
        "hide": 0,
        "label": "new_rollout_pod_template_hash",
        "name": "new_rollout_pod_template_hash",
        "options": [
          {
            "text": "65d74b6698",
            "value": "65d74b6698"
          }
        ],
        "query": "65d74b6698",
        "skipUrlSync": false,
        "type": "textbox"
      },
      {
        "current": {
          "text": "Prometheus",
          "value": "Prometheus"
        },
        "hide": 0,
        "label": "datasource",
        "name": "datasource",
        "options": [],
        "query": "prometheus",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "type": "datasource"
      }
    ]
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ],
    "time_options": [
      "5m",
      "15m",
      "1h",
      "6h",
      "12h",
      "24h",
      "2d",
      "7d",
      "30d"
    ]
  },
  "timezone": "",
  "title": "status_code_2xx",
  "uid": "devtron-app-metrics-2xx",
  "version": 1
}


// throughput

{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 17,
  "iteration": 1571208472797,
  "links": [],
  "panels": [
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "$datasource",
      "fill": 1,
      "gridPos": {
        "h": 9,
        "w": 12,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "links": [],
      "nullPointMode": "null",
      "percentage": false,
      "pointradius": 5,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "sum by(appId,rollouts_pod_template_hash, envId) (label_replace(rate(envoy_cluster_upstream_rq_total{appId=\"$app\", envId=\"$env\"}[1m]), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "{{rollouts_pod_template_hash}}",
          "refId": "A"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    }
  ],
  "refresh": "10s",
  "schemaVersion": 16,
  "style": "dark",
  "tags": [],
  "templating": {
    "list": [
      {
        "allValue": null,
        "current": {
          "text": "34",
          "value": "34"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_appId)",
        "hide": 0,
        "includeAll": false,
        "label": "app",
        "multi": false,
        "name": "app",
        "options": [],
        "query": "label_values(kube_pod_labels,label_appId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": null,
        "current": {
          "text": "1",
          "value": "1"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_envId)",
        "hide": 0,
        "includeAll": false,
        "label": "env",
        "multi": false,
        "name": "env",
        "options": [],
        "query": "label_values(kube_pod_labels,label_envId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "current": {
          "text": "84c89c85b7",
          "value": "84c89c85b7"
        },
        "hide": 0,
        "label": "new_rollout_pod_template_hash",
        "name": "new_rollout_pod_template_hash",
        "options": [
          {
            "text": "65d74b6698",
            "value": "65d74b6698"
          }
        ],
        "query": "65d74b6698",
        "skipUrlSync": false,
        "type": "textbox"
      },
      {
        "current": {
          "text": "Prometheus",
          "value": "Prometheus"
        },
        "hide": 0,
        "label": "datasource",
        "name": "datasource",
        "options": [],
        "query": "prometheus",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "type": "datasource"
      }
    ]
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ],
    "time_options": [
      "5m",
      "15m",
      "1h",
      "6h",
      "12h",
      "24h",
      "2d",
      "7d",
      "30d"
    ]
  },
  "timezone": "",
  "title": "throughput",
  "uid": "devtron-app-metrics-throughput",
  "version": 12
}

//4XX

{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 31,
  "iteration": 1571920354412,
  "links": [],
  "panels": [
    {
      "aliasColors": {},
      "bars": false,
      "dashLength": 10,
      "dashes": false,
      "datasource": "$datasource",
      "fill": 1,
      "gridPos": {
        "h": 9,
        "w": 12,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "legend": {
        "avg": false,
        "current": false,
        "max": false,
        "min": false,
        "show": true,
        "total": false,
        "values": false
      },
      "lines": true,
      "linewidth": 1,
      "links": [],
      "nullPointMode": "null",
      "percentage": false,
      "pointradius": 5,
      "points": false,
      "renderer": "flot",
      "seriesOverrides": [],
      "spaceLength": 10,
      "stack": false,
      "steppedLine": false,
      "targets": [
        {
          "expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(rate(envoy_cluster_upstream_rq{response_code_class=\"4xx\", appId=\"$app\", envId=\"$env\"}[1m]), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))",
          "format": "time_series",
          "hide": false,
          "intervalFactor": 1,
          "legendFormat": "{{rollouts_pod_template_hash}}",
          "refId": "A"
        }
      ],
      "thresholds": [],
      "timeFrom": null,
      "timeRegions": [],
      "timeShift": null,
      "title": "",
      "tooltip": {
        "shared": true,
        "sort": 0,
        "value_type": "individual"
      },
      "type": "graph",
      "xaxis": {
        "buckets": null,
        "mode": "time",
        "name": null,
        "show": true,
        "values": []
      },
      "yaxes": [
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        },
        {
          "format": "short",
          "label": null,
          "logBase": 1,
          "max": null,
          "min": null,
          "show": true
        }
      ],
      "yaxis": {
        "align": false,
        "alignLevel": null
      }
    }
  ],
  "refresh": false,
  "schemaVersion": 16,
  "style": "dark",
  "tags": [],
  "templating": {
    "list": [
      {
        "allValue": null,
        "current": {
          "text": "71",
          "value": "71"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_appId)",
        "hide": 0,
        "includeAll": false,
        "label": "app",
        "multi": false,
        "name": "app",
        "options": [],
        "query": "label_values(kube_pod_labels,label_appId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "allValue": null,
        "current": {
          "text": "1",
          "value": "1"
        },
        "datasource": "Prometheus",
        "definition": "label_values(kube_pod_labels,label_envId)",
        "hide": 0,
        "includeAll": false,
        "label": "env",
        "multi": false,
        "name": "env",
        "options": [],
        "query": "label_values(kube_pod_labels,label_envId)",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 0,
        "tagValuesQuery": "",
        "tags": [],
        "tagsQuery": "",
        "type": "query",
        "useTags": false
      },
      {
        "current": {
          "text": "84c89c85b7",
          "value": "84c89c85b7"
        },
        "hide": 0,
        "label": "new_rollout_pod_template_hash",
        "name": "new_rollout_pod_template_hash",
        "options": [
          {
            "text": "65d74b6698",
            "value": "65d74b6698"
          }
        ],
        "query": "65d74b6698",
        "skipUrlSync": false,
        "type": "textbox"
      },
      {
        "current": {
          "text": "Prometheus",
          "value": "Prometheus"
        },
        "hide": 0,
        "label": "datasource",
        "name": "datasource",
        "options": [],
        "query": "prometheus",
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "type": "datasource"
      }
    ]
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {
    "refresh_intervals": [
      "5s",
      "10s",
      "30s",
      "1m",
      "5m",
      "15m",
      "30m",
      "1h",
      "2h",
      "1d"
    ],
    "time_options": [
      "5m",
      "15m",
      "1h",
      "6h",
      "12h",
      "24h",
      "2d",
      "7d",
      "30d"
    ]
  },
  "timezone": "",
  "title": "status_code_4xx",
  "uid": "devtron-app-metrics-4xx",
  "version": 4
}
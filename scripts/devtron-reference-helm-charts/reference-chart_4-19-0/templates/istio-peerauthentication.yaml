{{- with .Values.istio }}
{{- if and .enable .peerAuthentication.enabled }}
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: {{  template ".Chart.Name .fullname" $ }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
    {{- if .peerAuthentication.labels }}
{{ toYaml .peerAuthentication.labels | indent 4 }}
    {{- end }}
{{- if .peerAuthentication.annotations }}
  annotations:
{{ toYaml .peerAuthentication.annotations | indent 4 }}
{{- end }}
spec:
{{- if .peerAuthentication.selector.enabled }}
  selector:
    matchLabels: 
      app.kubernetes.io/name: {{ template ".Chart.Name .fullname" $ }}
{{- end }}
  mtls:
    mode: {{ .peerAuthentication.mtls.mode }}
{{- if $.Values.istio.peerAuthentication.portLevelMtls }}
  portLevelMtls:
{{ toYaml $.Values.istio.peerAuthentication.portLevelMtls | indent 4 }}
{{- end }}
{{- end }}
{{- end }}
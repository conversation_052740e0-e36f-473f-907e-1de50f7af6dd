{{- define "job-template-spec" }}
{{- if $.Values.jobConfigs }}
{{- if $.Values.jobConfigs.backoffLimit }}
backoffLimit: {{ $.Values.jobConfigs.backoffLimit }}
{{- end }}
{{- if $.Values.jobConfigs.activeDeadlineSeconds }}
activeDeadlineSeconds: {{ $.Values.jobConfigs.activeDeadlineSeconds }}
{{- end }}
{{- if $.Values.jobConfigs.parallelism }}
parallelism: {{ $.Values.jobConfigs.parallelism }}
{{- end }}
{{- if $.Values.jobConfigs.completions }}
completions: {{ $.Values.jobConfigs.completions }}
{{- end }}
{{- if semverCompare ">1.20" .Capabilities.KubeVersion.GitVersion }}
{{- if $.Values.jobConfigs.suspend }}
suspend: {{ $.Values.jobConfigs.suspend }}
{{- end }}
{{- end }}
{{- if $.Values.jobConfigs.ttlSecondsAfterFinished }}
ttlSecondsAfterFinished: {{ $.Values.jobConfigs.ttlSecondsAfterFinished }}
{{- end }}
{{- end }}
template:
  metadata:
  {{- if $.Values.podAnnotations }}
    annotations:
    {{- range $key, $value := $.Values.podAnnotations }}
      {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
    labels:
      app: {{ template ".Chart.Name .name" $ }}
      appId: {{ $.Values.app | quote }}
      envId: {{ $.Values.env | quote }}
      release: {{ $.Release.Name }}
      {{- if $.Values.podLabels }}
{{ toYaml $.Values.podLabels | indent 6 }}
      {{- end }}
  spec:
    {{- include "pod-template-spec" . | indent 4 }}
{{- end }}
{{- if .Values.ConfigSecrets.enabled }}
  {{- range .Values.ConfigSecrets.secrets }}
  {{if eq .external true }}
  {{if (or (eq .externalType "ESO_GoogleSecretsManager") (eq .externalType "ESO_AWSSecretsManager") (eq .externalType "ESO_HashiCorpVault") (eq .externalType "ESO_AzureSecretsManager"))}}
---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: {{ .name }}
{{- if $.Values.appLabels }}
  labels:
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
spec:
  provider:
    {{- toYaml .esoSecretData.secretStore | nindent 4 }}

---
apiVersion: external-secrets.io/v1beta1 
kind: ExternalSecret
metadata:
  name: {{ .name }}
{{- if $.Values.appLabels }}
  labels:
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
spec:
  {{- if .refreshInterval }}
  refreshInterval: {{ .refreshInterval }}
  {{- else }}
  refreshInterval: 1h
  {{- end }}
  secretStoreRef:
    name: {{ .name }}
    kind: SecretStore
  target:
    name: {{ .name }}
    creationPolicy: Owner
  data:
  {{- range .esoSecretData.esoData }}
  - secretKey: {{ .secretKey }}
    remoteRef:
        key: {{ .key }}
        property: {{ .property }} 
  {{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
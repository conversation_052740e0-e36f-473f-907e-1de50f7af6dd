{{- if .Values.podDisruptionBudget }}
{{- if semverCompare ">=1.21-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: policy/v1
{{- else -}}
apiVersion: policy/v1beta1
{{- end }}
kind: PodDisruptionBudget
metadata:
  {{- if .Values.podDisruptionBudget.name }}
  name: {{ .Values.podDisruptionBudget.name }}
  {{- else }}
  name: {{ include ".Chart.Name .fullname" $ }}
  {{- end }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    {{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 4 }}
    {{- end }}        
spec:
  {{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels:
    {{- if .Values.customPodLabels }}
{{ toYaml .Values.customPodLabels | indent 6 }}  
    {{- else }}
      appId: {{ $.Values.app | quote }}
      envId: {{ $.Values.env | quote }}
    {{- end }}
  {{- end }}

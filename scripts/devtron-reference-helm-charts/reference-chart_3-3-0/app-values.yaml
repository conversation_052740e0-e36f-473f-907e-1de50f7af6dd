replicaCount: 1
MinReadySeconds: 60
GracePeriod: 30

LivenessProbe:
  Path: ""
  port: 8080
  initialDelaySeconds: 20
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5
  failureThreshold: 3

ReadinessProbe:
  Path: ""
  port: 8080
  initialDelaySeconds: 20
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5
  failureThreshold: 3

ContainerPort:
  - name: app
    port: 8080
    servicePort: 80

ingress:
  enabled: false
  labels: {}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/force-ssl-redirect: 'false'
    nginx.ingress.kubernetes.io/ssl-redirect: 'false'
    kubernetes.io/ingress.class: nginx
#     kubernetes.io/ingress.class: nginx
#     kubernetes.io/tls-acme: "true"
#    nginx.ingress.kubernetes.io/canary: "true"
#    nginx.ingress.kubernetes.io/canary-weight: "10"

  path: ""
  host: ""
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

ingressInternal:
  enabled: false
  annotations: {}
  #     kubernetes.io/ingress.class: nginx
  #     kubernetes.io/tls-acme: "true"
  #    nginx.ingress.kubernetes.io/canary: "true"
  #    nginx.ingress.kubernetes.io/canary-weight: "10"

  path: ""
  host: ""
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

volumeMounts: []
#     - name: log-volume
#       mountPath: /var/log

volumes: []
#     - name: log-volume
#       emptyDir: {}

image:
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  #  name: "1234567890123456789012345678901234567890123456789012345678901234567890"
  annotations: {}
    # test1: test2
    # test3: test4

prometheus:
  release: monitoring

Spec:
  Affinity:
    Key:
    #  Key: kops.k8s.io/instancegroup
    Values:

autoscaling:
  enabled: false
  MinReplicas: 1
  MaxReplicas: 2
  TargetCPUUtilizationPercentage: 90
  TargetMemoryUtilizationPercentage: 80
  annotations: {}
  labels: {}

server:
  deployment:
    image_tag: 1-95af053
    image: ""

servicemonitor:
  enabled: false

dbMigrationConfig:
  enabled: false

command:
  enabled: false
  value: []

args:
  enabled: false
  value: []

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1
    memory: 200Mi
  requests:
    cpu: 0.10
    memory: 100Mi

initContainers: []
  ## Additional init containers to run before the Scheduler pods.
  ## for example, be used to run a sidecar that chown Logs storage .
  #- name: volume-mount-hack
  #  image: busybox
  #  command: ["sh", "-c", "chown -R 1000:1000 logs"]
  #  volumeMounts:
  #    - mountPath: /usr/local/airflow/logs
  #      name: logs-data

containers: []
  ## Additional init containers to run before the Scheduler pods.
  ## for example, be used to run a sidecar that chown Logs storage .
  #- name: volume-mount-hack
  #  image: busybox
  #  command: ["sh", "-c", "chown -R 1000:1000 logs"]
  #  volumeMounts:
  #    - mountPath: /usr/local/airflow/logs
  #      name: logs-data


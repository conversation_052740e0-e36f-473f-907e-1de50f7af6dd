{{- if $.Values.autoscaling.enabled }}
---
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
 name: {{ template ".Chart.Name .fullname" $ }}-hpa
 {{- if .Values.autoscaling.annotations }}
 annotations:
{{ toYaml .Values.autoscaling.annotations | indent 4 }}
  {{- end }}
  {{- if .Values.autoscaling.labels }}
 labels:
{{ toYaml .Values.autoscaling.labels | indent 4 }}
  {{- end }}
spec:
 scaleTargetRef:
   apiVersion: argoproj.io/v1alpha1
   kind: Rollout
   name: {{ include ".Chart.Name .fullname" $ }}
 minReplicas: {{ $.Values.autoscaling.MinReplicas  }}
 maxReplicas: {{ $.Values.autoscaling.MaxReplicas }}
{{/* targetCPUUtilizationPercentage: {{ $.Values.autoscaling.TargetCPUUtilizationPercentage}} */}}
 metrics:
   - type: Resource
     resource:
       name: memory
       targetAverageUtilization: {{ $.Values.autoscaling.TargetCPUUtilizationPercentage }}
   - type: Resource
     resource:
       name: cpu
       targetAverageUtilization: {{ $.Values.autoscaling.TargetMemoryUtilizationPercentage }}
{{- end }}

{{- if $.Values.serviceAccount }}
{{- if $.Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "serviceAccountName" . }}
  labels:
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 4 }}
  {{- end }}
  {{- if .Values.serviceAccount.annotations }}
  annotations:
{{ toYaml .Values.serviceAccount.annotations | indent 4 }}
  {{- end }}
{{- end -}}
{{- end -}}

{{- if $.Values.appMetrics -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template ".Chart.Name .fullname" $ }}
  labels:
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ .Values.prometheus.release }}
spec:
  jobLabel: {{ template ".Chart.Name .name" $ }}
  endpoints:
    - port: envoy-admin
      interval: 30s
      path: /stats/prometheus
      relabelings:
        - action: replace
          regex: (.*)(\S{10})
          replacement: ${2}
          sourceLabels:
            - __meta_kubernetes_pod_label_controller_revision_hash
          targetLabel: statefulset_hash
        - action: replace
          sourceLabels:
            - statefulset_hash
          targetLabel: devtron_app_hash
  selector:
    matchLabels:
      app: {{ template ".Chart.Name .name" $ }}
      appId: {{ $.Values.app | quote }}
      envId: {{ $.Values.env | quote }}
  namespaceSelector:
    matchNames:
      - {{.Release.Namespace}}
  podTargetLabels:
    - appId
    - envId
    - controller-revision-hash
    - devtron_app_hash

{{- end }}

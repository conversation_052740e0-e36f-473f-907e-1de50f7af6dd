{{- with .Values.istio }}
{{- if and .enable .peerAuthentication.enabled }}
{{ if semverCompare ">=1.22-0" $.Capabilities.KubeVersion.GitVersion -}}
apiVersion: security.istio.io/v1
{{- else -}}
apiVersion: security.istio.io/v1beta1
{{- end }}
kind: PeerAuthentication
metadata:
  {{- if .peerAuthentication.name }}
  name: {{ .peerAuthentication.name }}
  {{- else }}
  name: {{  template ".Chart.Name .fullname" $ }}
  {{- end }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
    {{- if .peerAuthentication.labels }}
{{ toYaml .peerAuthentication.labels | indent 4 }}
    {{- end }}
{{- if .peerAuthentication.annotations }}
  annotations:
{{ toYaml .peerAuthentication.annotations | indent 4 }}
{{- end }}
spec:
{{- if .peerAuthentication.selector.enabled }}
  selector:
    matchLabels: 
      app.kubernetes.io/name: {{ template ".Chart.Name .fullname" $ }}
{{- end }}
  mtls:
    mode: {{ .peerAuthentication.mtls.mode }}
{{- if $.Values.istio.peerAuthentication.portLevelMtls }}
  portLevelMtls:
{{ toYaml $.Values.istio.peerAuthentication.portLevelMtls | indent 4 }}
{{- end }}
{{- end }}
{{- end }}
package tests

import (
	"github.com/devtron-labs/devtron/client/argocdServer/repoCredsK8sClient"
	"testing"
)

func Test_OCIArgoSecretRepoPathAndHostParseLogic(t *testing.T) {
	type args struct {
		repositoryURL  string
		repositoryName string
	}
	tests := []struct {
		name                 string
		args                 args
		expectedHost         string
		expectedFullRepoPath string
	}{
		{
			name: "case 1",
			args: args{
				repositoryURL:  "docker.io/bitnamicharts",
				repositoryName: "bitnami",
			},
			expectedHost:         "docker.io",
			expectedFullRepoPath: "bitnamicharts/bitnami",
		},
		{
			name: "case 2",
			args: args{
				repositoryURL:  "docker.io",
				repositoryName: "bitnamicharts/bitnami",
			},
			expectedHost:         "docker.io",
			expectedFullRepoPath: "bitnamicharts/bitnami",
		},
		{
			name: "case 3",
			args: args{
				repositoryURL:  "oci://docker.io",
				repositoryName: "bitnamicharts/bitnami",
			},
			expectedHost:         "docker.io",
			expectedFullRepoPath: "bitnamicharts/bitnami",
		},
		{
			name: "case 4",
			args: args{
				repositoryURL:  "https://**********/foo/bar",
				repositoryName: "chart",
			},
			expectedHost:         "**********",
			expectedFullRepoPath: "foo/bar/chart",
		},
		{
			name: "case 5",
			args: args{
				repositoryURL:  "http://**********/foo/bar",
				repositoryName: "chart",
			},
			expectedHost:         "**********",
			expectedFullRepoPath: "foo/bar/chart",
		},
		{
			name: "case 6",
			args: args{
				repositoryURL:  "https://**********",
				repositoryName: "foo/bar/chart",
			},
			expectedHost:         "**********",
			expectedFullRepoPath: "foo/bar/chart",
		},
		{
			name: "case 7",
			args: args{
				repositoryURL:  "http://**********",
				repositoryName: "foo/bar/chart",
			},
			expectedHost:         "**********",
			expectedFullRepoPath: "foo/bar/chart",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if host, fullRepoPath, err := repoCredsK8sClient.GetHostAndFullRepoPath(tt.args.repositoryURL, tt.args.repositoryName); err != nil || host != tt.expectedHost || fullRepoPath != tt.expectedFullRepoPath {
				t.Errorf("SanitizeRepoNameAndURLForOCIRepo() = repositoryURL: %v , repositoryName: %v, want  %v %v", host, fullRepoPath, tt.expectedHost, tt.expectedFullRepoPath)
			}
		})
	}
}

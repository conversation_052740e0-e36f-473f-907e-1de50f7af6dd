/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// ChartMetadata struct for ChartMetadata
type ChartMetadata struct {
	// name of the chart
	ChartName *string `json:"chartName,omitempty"`
	// version of the helm chart
	ChartVersion *string `json:"chartVersion,omitempty"`
	// chart home
	Home *string `json:"home,omitempty"`
	// source of the helm chart
	Sources *[]string `json:"sources,omitempty"`
	// description of the helm chart
	Description *string `json:"description,omitempty"`
}

// NewChartMetadata instantiates a new ChartMetadata object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChartMetadata() *ChartMetadata {
	this := ChartMetadata{}
	return &this
}

// NewChartMetadataWithDefaults instantiates a new ChartMetadata object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChartMetadataWithDefaults() *ChartMetadata {
	this := ChartMetadata{}
	return &this
}

// GetChartName returns the ChartName field value if set, zero value otherwise.
func (o *ChartMetadata) GetChartName() string {
	if o == nil || o.ChartName == nil {
		var ret string
		return ret
	}
	return *o.ChartName
}

// GetChartNameOk returns a tuple with the ChartName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChartMetadata) GetChartNameOk() (*string, bool) {
	if o == nil || o.ChartName == nil {
		return nil, false
	}
	return o.ChartName, true
}

// HasChartName returns a boolean if a field has been set.
func (o *ChartMetadata) HasChartName() bool {
	if o != nil && o.ChartName != nil {
		return true
	}

	return false
}

// SetChartName gets a reference to the given string and assigns it to the ChartName field.
func (o *ChartMetadata) SetChartName(v string) {
	o.ChartName = &v
}

// GetChartVersion returns the ChartVersion field value if set, zero value otherwise.
func (o *ChartMetadata) GetChartVersion() string {
	if o == nil || o.ChartVersion == nil {
		var ret string
		return ret
	}
	return *o.ChartVersion
}

// GetChartVersionOk returns a tuple with the ChartVersion field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChartMetadata) GetChartVersionOk() (*string, bool) {
	if o == nil || o.ChartVersion == nil {
		return nil, false
	}
	return o.ChartVersion, true
}

// HasChartVersion returns a boolean if a field has been set.
func (o *ChartMetadata) HasChartVersion() bool {
	if o != nil && o.ChartVersion != nil {
		return true
	}

	return false
}

// SetChartVersion gets a reference to the given string and assigns it to the ChartVersion field.
func (o *ChartMetadata) SetChartVersion(v string) {
	o.ChartVersion = &v
}

// GetHome returns the Home field value if set, zero value otherwise.
func (o *ChartMetadata) GetHome() string {
	if o == nil || o.Home == nil {
		var ret string
		return ret
	}
	return *o.Home
}

// GetHomeOk returns a tuple with the Home field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChartMetadata) GetHomeOk() (*string, bool) {
	if o == nil || o.Home == nil {
		return nil, false
	}
	return o.Home, true
}

// HasHome returns a boolean if a field has been set.
func (o *ChartMetadata) HasHome() bool {
	if o != nil && o.Home != nil {
		return true
	}

	return false
}

// SetHome gets a reference to the given string and assigns it to the Home field.
func (o *ChartMetadata) SetHome(v string) {
	o.Home = &v
}

// GetSources returns the Sources field value if set, zero value otherwise.
func (o *ChartMetadata) GetSources() []string {
	if o == nil || o.Sources == nil {
		var ret []string
		return ret
	}
	return *o.Sources
}

// GetSourcesOk returns a tuple with the Sources field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChartMetadata) GetSourcesOk() (*[]string, bool) {
	if o == nil || o.Sources == nil {
		return nil, false
	}
	return o.Sources, true
}

// HasSources returns a boolean if a field has been set.
func (o *ChartMetadata) HasSources() bool {
	if o != nil && o.Sources != nil {
		return true
	}

	return false
}

// SetSources gets a reference to the given []string and assigns it to the Sources field.
func (o *ChartMetadata) SetSources(v []string) {
	o.Sources = &v
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *ChartMetadata) GetDescription() string {
	if o == nil || o.Description == nil {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChartMetadata) GetDescriptionOk() (*string, bool) {
	if o == nil || o.Description == nil {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *ChartMetadata) HasDescription() bool {
	if o != nil && o.Description != nil {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *ChartMetadata) SetDescription(v string) {
	o.Description = &v
}

func (o ChartMetadata) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ChartName != nil {
		toSerialize["chartName"] = o.ChartName
	}
	if o.ChartVersion != nil {
		toSerialize["chartVersion"] = o.ChartVersion
	}
	if o.Home != nil {
		toSerialize["home"] = o.Home
	}
	if o.Sources != nil {
		toSerialize["sources"] = o.Sources
	}
	if o.Description != nil {
		toSerialize["description"] = o.Description
	}
	return json.Marshal(toSerialize)
}

type NullableChartMetadata struct {
	value *ChartMetadata
	isSet bool
}

func (v NullableChartMetadata) Get() *ChartMetadata {
	return v.value
}

func (v *NullableChartMetadata) Set(val *ChartMetadata) {
	v.value = val
	v.isSet = true
}

func (v NullableChartMetadata) IsSet() bool {
	return v.isSet
}

func (v *NullableChartMetadata) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChartMetadata(val *ChartMetadata) *NullableChartMetadata {
	return &NullableChartMetadata{value: val, isSet: true}
}

func (v NullableChartMetadata) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChartMetadata) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}



/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// DesiredManifestResponse struct for DesiredManifestResponse
type DesiredManifestResponse struct {
	// desired manifest
	Manifest *string `json:"manifest,omitempty"`
}

// NewDesiredManifestResponse instantiates a new DesiredManifestResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDesiredManifestResponse() *DesiredManifestResponse {
	this := DesiredManifestResponse{}
	return &this
}

// NewDesiredManifestResponseWithDefaults instantiates a new DesiredManifestResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDesiredManifestResponseWithDefaults() *DesiredManifestResponse {
	this := DesiredManifestResponse{}
	return &this
}

// GetManifest returns the Manifest field value if set, zero value otherwise.
func (o *DesiredManifestResponse) GetManifest() string {
	if o == nil || o.Manifest == nil {
		var ret string
		return ret
	}
	return *o.Manifest
}

// GetManifestOk returns a tuple with the Manifest field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DesiredManifestResponse) GetManifestOk() (*string, bool) {
	if o == nil || o.Manifest == nil {
		return nil, false
	}
	return o.Manifest, true
}

// HasManifest returns a boolean if a field has been set.
func (o *DesiredManifestResponse) HasManifest() bool {
	if o != nil && o.Manifest != nil {
		return true
	}

	return false
}

// SetManifest gets a reference to the given string and assigns it to the Manifest field.
func (o *DesiredManifestResponse) SetManifest(v string) {
	o.Manifest = &v
}

func (o DesiredManifestResponse) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Manifest != nil {
		toSerialize["manifest"] = o.Manifest
	}
	return json.Marshal(toSerialize)
}

type NullableDesiredManifestResponse struct {
	value *DesiredManifestResponse
	isSet bool
}

func (v NullableDesiredManifestResponse) Get() *DesiredManifestResponse {
	return v.value
}

func (v *NullableDesiredManifestResponse) Set(val *DesiredManifestResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableDesiredManifestResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableDesiredManifestResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDesiredManifestResponse(val *DesiredManifestResponse) *NullableDesiredManifestResponse {
	return &NullableDesiredManifestResponse{value: val, isSet: true}
}

func (v NullableDesiredManifestResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDesiredManifestResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}



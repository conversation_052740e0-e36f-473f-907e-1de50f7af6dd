/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// ClusterEnvironmentDetail struct for ClusterEnvironmentDetail
type ClusterEnvironmentDetail struct {
	// name of the cluster
	ClusterName *string `json:"clusterName,omitempty"`
	// id of the cluster
	ClusterID *int32 `json:"clusterID,omitempty"`
	// environments in that cluster
	Environments *[]EnvironmentDetail `json:"environments,omitempty"`
}

// NewClusterEnvironmentDetail instantiates a new ClusterEnvironmentDetail object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewClusterEnvironmentDetail() *ClusterEnvironmentDetail {
	this := ClusterEnvironmentDetail{}
	return &this
}

// NewClusterEnvironmentDetailWithDefaults instantiates a new ClusterEnvironmentDetail object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewClusterEnvironmentDetailWithDefaults() *ClusterEnvironmentDetail {
	this := ClusterEnvironmentDetail{}
	return &this
}

// GetClusterName returns the ClusterName field value if set, zero value otherwise.
func (o *ClusterEnvironmentDetail) GetClusterName() string {
	if o == nil || o.ClusterName == nil {
		var ret string
		return ret
	}
	return *o.ClusterName
}

// GetClusterNameOk returns a tuple with the ClusterName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ClusterEnvironmentDetail) GetClusterNameOk() (*string, bool) {
	if o == nil || o.ClusterName == nil {
		return nil, false
	}
	return o.ClusterName, true
}

// HasClusterName returns a boolean if a field has been set.
func (o *ClusterEnvironmentDetail) HasClusterName() bool {
	if o != nil && o.ClusterName != nil {
		return true
	}

	return false
}

// SetClusterName gets a reference to the given string and assigns it to the ClusterName field.
func (o *ClusterEnvironmentDetail) SetClusterName(v string) {
	o.ClusterName = &v
}

// GetClusterID returns the ClusterID field value if set, zero value otherwise.
func (o *ClusterEnvironmentDetail) GetClusterID() int32 {
	if o == nil || o.ClusterID == nil {
		var ret int32
		return ret
	}
	return *o.ClusterID
}

// GetClusterIDOk returns a tuple with the ClusterID field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ClusterEnvironmentDetail) GetClusterIDOk() (*int32, bool) {
	if o == nil || o.ClusterID == nil {
		return nil, false
	}
	return o.ClusterID, true
}

// HasClusterID returns a boolean if a field has been set.
func (o *ClusterEnvironmentDetail) HasClusterID() bool {
	if o != nil && o.ClusterID != nil {
		return true
	}

	return false
}

// SetClusterID gets a reference to the given int32 and assigns it to the ClusterID field.
func (o *ClusterEnvironmentDetail) SetClusterID(v int32) {
	o.ClusterID = &v
}

// GetEnvironments returns the Environments field value if set, zero value otherwise.
func (o *ClusterEnvironmentDetail) GetEnvironments() []EnvironmentDetail {
	if o == nil || o.Environments == nil {
		var ret []EnvironmentDetail
		return ret
	}
	return *o.Environments
}

// GetEnvironmentsOk returns a tuple with the Environments field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ClusterEnvironmentDetail) GetEnvironmentsOk() (*[]EnvironmentDetail, bool) {
	if o == nil || o.Environments == nil {
		return nil, false
	}
	return o.Environments, true
}

// HasEnvironments returns a boolean if a field has been set.
func (o *ClusterEnvironmentDetail) HasEnvironments() bool {
	if o != nil && o.Environments != nil {
		return true
	}

	return false
}

// SetEnvironments gets a reference to the given []EnvironmentDetail and assigns it to the Environments field.
func (o *ClusterEnvironmentDetail) SetEnvironments(v []EnvironmentDetail) {
	o.Environments = &v
}

func (o ClusterEnvironmentDetail) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ClusterName != nil {
		toSerialize["clusterName"] = o.ClusterName
	}
	if o.ClusterID != nil {
		toSerialize["clusterID"] = o.ClusterID
	}
	if o.Environments != nil {
		toSerialize["environments"] = o.Environments
	}
	return json.Marshal(toSerialize)
}

type NullableClusterEnvironmentDetail struct {
	value *ClusterEnvironmentDetail
	isSet bool
}

func (v NullableClusterEnvironmentDetail) Get() *ClusterEnvironmentDetail {
	return v.value
}

func (v *NullableClusterEnvironmentDetail) Set(val *ClusterEnvironmentDetail) {
	v.value = val
	v.isSet = true
}

func (v NullableClusterEnvironmentDetail) IsSet() bool {
	return v.isSet
}

func (v *NullableClusterEnvironmentDetail) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableClusterEnvironmentDetail(val *ClusterEnvironmentDetail) *NullableClusterEnvironmentDetail {
	return &NullableClusterEnvironmentDetail{value: val, isSet: true}
}

func (v NullableClusterEnvironmentDetail) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableClusterEnvironmentDetail) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}

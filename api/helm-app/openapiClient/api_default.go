/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"bytes"
	_context "context"
	_ioutil "io/ioutil"
	_nethttp "net/http"
	_neturl "net/url"
	"reflect"
)

// Linger please
var (
	_ _context.Context
)

// DefaultApiService DefaultApi service
type DefaultApiService service

type ApiOrchestratorAppListGetRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	projectIds *[]int32
	clusterIds *[]int32
	environmentIds *[]int32
	offset *int32
	size *int32
	sortOrder *string
	sortBy *string
}

// project ids
func (r ApiOrchestratorAppListGetRequest) ProjectIds(projectIds []int32) ApiOrchestratorAppListGetRequest {
	r.projectIds = &projectIds
	return r
}
// cluster ids
func (r ApiOrchestratorAppListGetRequest) ClusterIds(clusterIds []int32) ApiOrchestratorAppListGetRequest {
	r.clusterIds = &clusterIds
	return r
}
// environment ids
func (r ApiOrchestratorAppListGetRequest) EnvironmentIds(environmentIds []int32) ApiOrchestratorAppListGetRequest {
	r.environmentIds = &environmentIds
	return r
}
// offser
func (r ApiOrchestratorAppListGetRequest) Offset(offset int32) ApiOrchestratorAppListGetRequest {
	r.offset = &offset
	return r
}
// size
func (r ApiOrchestratorAppListGetRequest) Size(size int32) ApiOrchestratorAppListGetRequest {
	r.size = &size
	return r
}
// sortOrder
func (r ApiOrchestratorAppListGetRequest) SortOrder(sortOrder string) ApiOrchestratorAppListGetRequest {
	r.sortOrder = &sortOrder
	return r
}
// sortBy
func (r ApiOrchestratorAppListGetRequest) SortBy(sortBy string) ApiOrchestratorAppListGetRequest {
	r.sortBy = &sortBy
	return r
}

func (r ApiOrchestratorAppListGetRequest) Execute() (AppList, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorAppListGetExecute(r)
}

/*
OrchestratorAppListGet Method for OrchestratorAppListGet

this api gives all devtron applications.

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorAppListGetRequest
*/
func (a *DefaultApiService) OrchestratorAppListGet(ctx _context.Context) ApiOrchestratorAppListGetRequest {
	return ApiOrchestratorAppListGetRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return AppList
func (a *DefaultApiService) OrchestratorAppListGetExecute(r ApiOrchestratorAppListGetRequest) (AppList, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodGet
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  AppList
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorAppListGet")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/app/list/"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}
	if r.projectIds == nil {
		return localVarReturnValue, nil, reportError("projectIds is required and must be specified")
	}
	if r.clusterIds == nil {
		return localVarReturnValue, nil, reportError("clusterIds is required and must be specified")
	}
	if r.environmentIds == nil {
		return localVarReturnValue, nil, reportError("environmentIds is required and must be specified")
	}
	if r.offset == nil {
		return localVarReturnValue, nil, reportError("offset is required and must be specified")
	}
	if r.size == nil {
		return localVarReturnValue, nil, reportError("size is required and must be specified")
	}
	if r.sortOrder == nil {
		return localVarReturnValue, nil, reportError("sortOrder is required and must be specified")
	}
	if r.sortBy == nil {
		return localVarReturnValue, nil, reportError("sortBy is required and must be specified")
	}

	{
		t := *r.projectIds
		if reflect.TypeOf(t).Kind() == reflect.Slice {
			s := reflect.ValueOf(t)
			for i := 0; i < s.Len(); i++ {
				localVarQueryParams.Add("projectIds", parameterToString(s.Index(i), "multi"))
			}
		} else {
			localVarQueryParams.Add("projectIds", parameterToString(t, "multi"))
		}
	}
	{
		t := *r.clusterIds
		if reflect.TypeOf(t).Kind() == reflect.Slice {
			s := reflect.ValueOf(t)
			for i := 0; i < s.Len(); i++ {
				localVarQueryParams.Add("clusterIds", parameterToString(s.Index(i), "multi"))
			}
		} else {
			localVarQueryParams.Add("clusterIds", parameterToString(t, "multi"))
		}
	}
	{
		t := *r.environmentIds
		if reflect.TypeOf(t).Kind() == reflect.Slice {
			s := reflect.ValueOf(t)
			for i := 0; i < s.Len(); i++ {
				localVarQueryParams.Add("environmentIds", parameterToString(s.Index(i), "multi"))
			}
		} else {
			localVarQueryParams.Add("environmentIds", parameterToString(t, "multi"))
		}
	}
	localVarQueryParams.Add("offset", parameterToString(*r.offset, ""))
	localVarQueryParams.Add("size", parameterToString(*r.size, ""))
	localVarQueryParams.Add("sortOrder", parameterToString(*r.sortOrder, ""))
	localVarQueryParams.Add("sortBy", parameterToString(*r.sortBy, ""))
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorAppStoreInstalledAppsGetRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	envIds *[]int32
	clusterIds *[]int32
	onlyDeprecated *bool
	chartRepoIds *int32
	offset *int32
	size *int32
	appStoreName *string
	sortBy *string
}

// environment ids
func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) EnvIds(envIds []int32) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	r.envIds = &envIds
	return r
}
// cluster ids
func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) ClusterIds(clusterIds []int32) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	r.clusterIds = &clusterIds
	return r
}
// deprecated flag
func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) OnlyDeprecated(onlyDeprecated bool) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	r.onlyDeprecated = &onlyDeprecated
	return r
}
// size
func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) ChartRepoIds(chartRepoIds int32) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	r.chartRepoIds = &chartRepoIds
	return r
}
// offser
func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) Offset(offset int32) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	r.offset = &offset
	return r
}
// size
func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) Size(size int32) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	r.size = &size
	return r
}
// app store name
func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) AppStoreName(appStoreName string) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	r.appStoreName = &appStoreName
	return r
}
// app name
func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) SortBy(sortBy string) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	r.sortBy = &sortBy
	return r
}

func (r ApiOrchestratorAppStoreInstalledAppsGetRequest) Execute() (AppList, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorAppStoreInstalledAppsGetExecute(r)
}

/*
OrchestratorAppStoreInstalledAppsGet Method for OrchestratorAppStoreInstalledAppsGet

this api gives all chart-store applications.

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorAppStoreInstalledAppsGetRequest
*/
func (a *DefaultApiService) OrchestratorAppStoreInstalledAppsGet(ctx _context.Context) ApiOrchestratorAppStoreInstalledAppsGetRequest {
	return ApiOrchestratorAppStoreInstalledAppsGetRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return AppList
func (a *DefaultApiService) OrchestratorAppStoreInstalledAppsGetExecute(r ApiOrchestratorAppStoreInstalledAppsGetRequest) (AppList, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodGet
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  AppList
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorAppStoreInstalledAppsGet")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/app-store/installed-apps/"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}
	if r.envIds == nil {
		return localVarReturnValue, nil, reportError("envIds is required and must be specified")
	}
	if r.clusterIds == nil {
		return localVarReturnValue, nil, reportError("clusterIds is required and must be specified")
	}
	if r.onlyDeprecated == nil {
		return localVarReturnValue, nil, reportError("onlyDeprecated is required and must be specified")
	}
	if r.chartRepoIds == nil {
		return localVarReturnValue, nil, reportError("chartRepoIds is required and must be specified")
	}
	if r.offset == nil {
		return localVarReturnValue, nil, reportError("offset is required and must be specified")
	}
	if r.size == nil {
		return localVarReturnValue, nil, reportError("size is required and must be specified")
	}
	if r.appStoreName == nil {
		return localVarReturnValue, nil, reportError("appStoreName is required and must be specified")
	}
	if r.sortBy == nil {
		return localVarReturnValue, nil, reportError("sortBy is required and must be specified")
	}

	{
		t := *r.envIds
		if reflect.TypeOf(t).Kind() == reflect.Slice {
			s := reflect.ValueOf(t)
			for i := 0; i < s.Len(); i++ {
				localVarQueryParams.Add("envIds", parameterToString(s.Index(i), "multi"))
			}
		} else {
			localVarQueryParams.Add("envIds", parameterToString(t, "multi"))
		}
	}
	{
		t := *r.clusterIds
		if reflect.TypeOf(t).Kind() == reflect.Slice {
			s := reflect.ValueOf(t)
			for i := 0; i < s.Len(); i++ {
				localVarQueryParams.Add("clusterIds", parameterToString(s.Index(i), "multi"))
			}
		} else {
			localVarQueryParams.Add("clusterIds", parameterToString(t, "multi"))
		}
	}
	localVarQueryParams.Add("onlyDeprecated", parameterToString(*r.onlyDeprecated, ""))
	localVarQueryParams.Add("chartRepoIds", parameterToString(*r.chartRepoIds, ""))
	localVarQueryParams.Add("offset", parameterToString(*r.offset, ""))
	localVarQueryParams.Add("size", parameterToString(*r.size, ""))
	localVarQueryParams.Add("appStoreName", parameterToString(*r.appStoreName, ""))
	localVarQueryParams.Add("sortBy", parameterToString(*r.sortBy, ""))
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationClusterEnvDetailsGetRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
}


func (r ApiOrchestratorApplicationClusterEnvDetailsGetRequest) Execute() ([]ClusterEnvironmentDetail, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationClusterEnvDetailsGetExecute(r)
}

/*
OrchestratorApplicationClusterEnvDetailsGet Method for OrchestratorApplicationClusterEnvDetailsGet

returns cluster environment namespace mappings

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationClusterEnvDetailsGetRequest
*/
func (a *DefaultApiService) OrchestratorApplicationClusterEnvDetailsGet(ctx _context.Context) ApiOrchestratorApplicationClusterEnvDetailsGetRequest {
	return ApiOrchestratorApplicationClusterEnvDetailsGetRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return []ClusterEnvironmentDetail
func (a *DefaultApiService) OrchestratorApplicationClusterEnvDetailsGetExecute(r ApiOrchestratorApplicationClusterEnvDetailsGetRequest) ([]ClusterEnvironmentDetail, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodGet
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  []ClusterEnvironmentDetail
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationClusterEnvDetailsGet")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/cluster-env-details"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationDeleteDeleteRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	appId *string
}

// application Id
func (r ApiOrchestratorApplicationDeleteDeleteRequest) AppId(appId string) ApiOrchestratorApplicationDeleteDeleteRequest {
	r.appId = &appId
	return r
}

func (r ApiOrchestratorApplicationDeleteDeleteRequest) Execute() (UninstallReleaseResponse, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationDeleteDeleteExecute(r)
}

/*
OrchestratorApplicationDeleteDelete Method for OrchestratorApplicationDeleteDelete

delete application

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationDeleteDeleteRequest
*/
func (a *DefaultApiService) OrchestratorApplicationDeleteDelete(ctx _context.Context) ApiOrchestratorApplicationDeleteDeleteRequest {
	return ApiOrchestratorApplicationDeleteDeleteRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return UninstallReleaseResponse
func (a *DefaultApiService) OrchestratorApplicationDeleteDeleteExecute(r ApiOrchestratorApplicationDeleteDeleteRequest) (UninstallReleaseResponse, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodDelete
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  UninstallReleaseResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationDeleteDelete")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/delete"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}
	if r.appId == nil {
		return localVarReturnValue, nil, reportError("appId is required and must be specified")
	}

	localVarQueryParams.Add("appId", parameterToString(*r.appId, ""))
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationDeploymentDetailGetRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	appId *string
	version *int32
}

// project ids
func (r ApiOrchestratorApplicationDeploymentDetailGetRequest) AppId(appId string) ApiOrchestratorApplicationDeploymentDetailGetRequest {
	r.appId = &appId
	return r
}
// deployment version
func (r ApiOrchestratorApplicationDeploymentDetailGetRequest) Version(version int32) ApiOrchestratorApplicationDeploymentDetailGetRequest {
	r.version = &version
	return r
}

func (r ApiOrchestratorApplicationDeploymentDetailGetRequest) Execute() (HelmAppDeploymentManifestDetail, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationDeploymentDetailGetExecute(r)
}

/*
OrchestratorApplicationDeploymentDetailGet Method for OrchestratorApplicationDeploymentDetailGet

deployment details of helm app

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationDeploymentDetailGetRequest
*/
func (a *DefaultApiService) OrchestratorApplicationDeploymentDetailGet(ctx _context.Context) ApiOrchestratorApplicationDeploymentDetailGetRequest {
	return ApiOrchestratorApplicationDeploymentDetailGetRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return HelmAppDeploymentManifestDetail
func (a *DefaultApiService) OrchestratorApplicationDeploymentDetailGetExecute(r ApiOrchestratorApplicationDeploymentDetailGetRequest) (HelmAppDeploymentManifestDetail, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodGet
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  HelmAppDeploymentManifestDetail
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationDeploymentDetailGet")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/deployment-detail"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}
	if r.appId == nil {
		return localVarReturnValue, nil, reportError("appId is required and must be specified")
	}
	if r.version == nil {
		return localVarReturnValue, nil, reportError("version is required and must be specified")
	}

	localVarQueryParams.Add("appId", parameterToString(*r.appId, ""))
	localVarQueryParams.Add("version", parameterToString(*r.version, ""))
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationDeploymentHistoryGetRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	appId *string
}

// project ids
func (r ApiOrchestratorApplicationDeploymentHistoryGetRequest) AppId(appId string) ApiOrchestratorApplicationDeploymentHistoryGetRequest {
	r.appId = &appId
	return r
}

func (r ApiOrchestratorApplicationDeploymentHistoryGetRequest) Execute() ([]HelmAppDeploymentDetail, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationDeploymentHistoryGetExecute(r)
}

/*
OrchestratorApplicationDeploymentHistoryGet Method for OrchestratorApplicationDeploymentHistoryGet

deployment history of helm app

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationDeploymentHistoryGetRequest
*/
func (a *DefaultApiService) OrchestratorApplicationDeploymentHistoryGet(ctx _context.Context) ApiOrchestratorApplicationDeploymentHistoryGetRequest {
	return ApiOrchestratorApplicationDeploymentHistoryGetRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return []HelmAppDeploymentDetail
func (a *DefaultApiService) OrchestratorApplicationDeploymentHistoryGetExecute(r ApiOrchestratorApplicationDeploymentHistoryGetRequest) ([]HelmAppDeploymentDetail, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodGet
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  []HelmAppDeploymentDetail
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationDeploymentHistoryGet")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/deployment-history"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}
	if r.appId == nil {
		return localVarReturnValue, nil, reportError("appId is required and must be specified")
	}

	localVarQueryParams.Add("appId", parameterToString(*r.appId, ""))
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationDesiredManifestGetRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	desiredManifestRequest *DesiredManifestRequest
}

func (r ApiOrchestratorApplicationDesiredManifestGetRequest) DesiredManifestRequest(desiredManifestRequest DesiredManifestRequest) ApiOrchestratorApplicationDesiredManifestGetRequest {
	r.desiredManifestRequest = &desiredManifestRequest
	return r
}

func (r ApiOrchestratorApplicationDesiredManifestGetRequest) Execute() ([]DesiredManifestResponse, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationDesiredManifestGetExecute(r)
}

/*
OrchestratorApplicationDesiredManifestGet Method for OrchestratorApplicationDesiredManifestGet

get desired manifest for an object

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationDesiredManifestGetRequest
*/
func (a *DefaultApiService) OrchestratorApplicationDesiredManifestGet(ctx _context.Context) ApiOrchestratorApplicationDesiredManifestGetRequest {
	return ApiOrchestratorApplicationDesiredManifestGetRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return []DesiredManifestResponse
func (a *DefaultApiService) OrchestratorApplicationDesiredManifestGetExecute(r ApiOrchestratorApplicationDesiredManifestGetRequest) ([]DesiredManifestResponse, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodGet
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  []DesiredManifestResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationDesiredManifestGet")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/desired-manifest"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.desiredManifestRequest
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationHibernatePostRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	hibernateRequest *HibernateRequest
}

func (r ApiOrchestratorApplicationHibernatePostRequest) HibernateRequest(hibernateRequest HibernateRequest) ApiOrchestratorApplicationHibernatePostRequest {
	r.hibernateRequest = &hibernateRequest
	return r
}

func (r ApiOrchestratorApplicationHibernatePostRequest) Execute() ([]HibernateStatus, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationHibernatePostExecute(r)
}

/*
OrchestratorApplicationHibernatePost Method for OrchestratorApplicationHibernatePost

hibernate the app

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationHibernatePostRequest
*/
func (a *DefaultApiService) OrchestratorApplicationHibernatePost(ctx _context.Context) ApiOrchestratorApplicationHibernatePostRequest {
	return ApiOrchestratorApplicationHibernatePostRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return []HibernateStatus
func (a *DefaultApiService) OrchestratorApplicationHibernatePostExecute(r ApiOrchestratorApplicationHibernatePostRequest) ([]HibernateStatus, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodPost
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  []HibernateStatus
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationHibernatePost")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/hibernate"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.hibernateRequest
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationPostRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	helmAppListRequest *HelmAppListRequest
}

// json as request body
func (r ApiOrchestratorApplicationPostRequest) HelmAppListRequest(helmAppListRequest HelmAppListRequest) ApiOrchestratorApplicationPostRequest {
	r.helmAppListRequest = &helmAppListRequest
	return r
}

func (r ApiOrchestratorApplicationPostRequest) Execute() (AppList, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationPostExecute(r)
}

/*
OrchestratorApplicationPost Method for OrchestratorApplicationPost

this api gives all external application+ devtron helm chart applications.

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationPostRequest
*/
func (a *DefaultApiService) OrchestratorApplicationPost(ctx _context.Context) ApiOrchestratorApplicationPostRequest {
	return ApiOrchestratorApplicationPostRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return AppList
func (a *DefaultApiService) OrchestratorApplicationPostExecute(r ApiOrchestratorApplicationPostRequest) (AppList, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodPost
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  AppList
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationPost")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}
	if r.helmAppListRequest == nil {
		return localVarReturnValue, nil, reportError("helmAppListRequest is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"text/event-stream"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.helmAppListRequest
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationReleaseInfoGetRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	appId *string
}

// project ids
func (r ApiOrchestratorApplicationReleaseInfoGetRequest) AppId(appId string) ApiOrchestratorApplicationReleaseInfoGetRequest {
	r.appId = &appId
	return r
}

func (r ApiOrchestratorApplicationReleaseInfoGetRequest) Execute() (ReleaseAndInstalledAppInfo, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationReleaseInfoGetExecute(r)
}

/*
OrchestratorApplicationReleaseInfoGet Method for OrchestratorApplicationReleaseInfoGet

deployment values.yaml/release-info

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationReleaseInfoGetRequest
*/
func (a *DefaultApiService) OrchestratorApplicationReleaseInfoGet(ctx _context.Context) ApiOrchestratorApplicationReleaseInfoGetRequest {
	return ApiOrchestratorApplicationReleaseInfoGetRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return ReleaseAndInstalledAppInfo
func (a *DefaultApiService) OrchestratorApplicationReleaseInfoGetExecute(r ApiOrchestratorApplicationReleaseInfoGetRequest) (ReleaseAndInstalledAppInfo, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodGet
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  ReleaseAndInstalledAppInfo
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationReleaseInfoGet")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/release-info"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}
	if r.appId == nil {
		return localVarReturnValue, nil, reportError("appId is required and must be specified")
	}

	localVarQueryParams.Add("appId", parameterToString(*r.appId, ""))
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationUnhibernatePostRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	hibernateRequest *HibernateRequest
}

func (r ApiOrchestratorApplicationUnhibernatePostRequest) HibernateRequest(hibernateRequest HibernateRequest) ApiOrchestratorApplicationUnhibernatePostRequest {
	r.hibernateRequest = &hibernateRequest
	return r
}

func (r ApiOrchestratorApplicationUnhibernatePostRequest) Execute() ([]HibernateStatus, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationUnhibernatePostExecute(r)
}

/*
OrchestratorApplicationUnhibernatePost Method for OrchestratorApplicationUnhibernatePost

un hibernate the app

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationUnhibernatePostRequest
*/
func (a *DefaultApiService) OrchestratorApplicationUnhibernatePost(ctx _context.Context) ApiOrchestratorApplicationUnhibernatePostRequest {
	return ApiOrchestratorApplicationUnhibernatePostRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return []HibernateStatus
func (a *DefaultApiService) OrchestratorApplicationUnhibernatePostExecute(r ApiOrchestratorApplicationUnhibernatePostRequest) ([]HibernateStatus, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodPost
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  []HibernateStatus
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationUnhibernatePost")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/unhibernate"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.hibernateRequest
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationUpdatePutRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	updateReleaseRequest *UpdateReleaseRequest
}

func (r ApiOrchestratorApplicationUpdatePutRequest) UpdateReleaseRequest(updateReleaseRequest UpdateReleaseRequest) ApiOrchestratorApplicationUpdatePutRequest {
	r.updateReleaseRequest = &updateReleaseRequest
	return r
}

func (r ApiOrchestratorApplicationUpdatePutRequest) Execute() ([]UpdateReleaseResponse, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationUpdatePutExecute(r)
}

/*
OrchestratorApplicationUpdatePut Method for OrchestratorApplicationUpdatePut

update the application

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationUpdatePutRequest
*/
func (a *DefaultApiService) OrchestratorApplicationUpdatePut(ctx _context.Context) ApiOrchestratorApplicationUpdatePutRequest {
	return ApiOrchestratorApplicationUpdatePutRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return []UpdateReleaseResponse
func (a *DefaultApiService) OrchestratorApplicationUpdatePutExecute(r ApiOrchestratorApplicationUpdatePutRequest) ([]UpdateReleaseResponse, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodPut
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  []UpdateReleaseResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationUpdatePut")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/update"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.updateReleaseRequest
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiOrchestratorApplicationUpdateWithChartLinkingPutRequest struct {
	ctx _context.Context
	ApiService *DefaultApiService
	updateReleaseWithChartLinkingRequest *UpdateReleaseWithChartLinkingRequest
}

func (r ApiOrchestratorApplicationUpdateWithChartLinkingPutRequest) UpdateReleaseWithChartLinkingRequest(updateReleaseWithChartLinkingRequest UpdateReleaseWithChartLinkingRequest) ApiOrchestratorApplicationUpdateWithChartLinkingPutRequest {
	r.updateReleaseWithChartLinkingRequest = &updateReleaseWithChartLinkingRequest
	return r
}

func (r ApiOrchestratorApplicationUpdateWithChartLinkingPutRequest) Execute() (UpdateReleaseResponse, *_nethttp.Response, error) {
	return r.ApiService.OrchestratorApplicationUpdateWithChartLinkingPutExecute(r)
}

/*
OrchestratorApplicationUpdateWithChartLinkingPut Method for OrchestratorApplicationUpdateWithChartLinkingPut

update the application with chartstore linking

 @param ctx _context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiOrchestratorApplicationUpdateWithChartLinkingPutRequest
*/
func (a *DefaultApiService) OrchestratorApplicationUpdateWithChartLinkingPut(ctx _context.Context) ApiOrchestratorApplicationUpdateWithChartLinkingPutRequest {
	return ApiOrchestratorApplicationUpdateWithChartLinkingPutRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
//  @return UpdateReleaseResponse
func (a *DefaultApiService) OrchestratorApplicationUpdateWithChartLinkingPutExecute(r ApiOrchestratorApplicationUpdateWithChartLinkingPutRequest) (UpdateReleaseResponse, *_nethttp.Response, error) {
	var (
		localVarHTTPMethod   = _nethttp.MethodPut
		localVarPostBody     interface{}
		formFiles            []formFile
		localVarReturnValue  UpdateReleaseResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DefaultApiService.OrchestratorApplicationUpdateWithChartLinkingPut")
	if err != nil {
		return localVarReturnValue, nil, GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/orchestrator/application/update-with-chart-linking"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := _neturl.Values{}
	localVarFormParams := _neturl.Values{}
	if r.updateReleaseWithChartLinkingRequest == nil {
		return localVarReturnValue, nil, reportError("updateReleaseWithChartLinkingRequest is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.updateReleaseWithChartLinkingRequest
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := _ioutil.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = _ioutil.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

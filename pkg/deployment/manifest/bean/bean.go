/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bean

import (
	"github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
)

const (
	FullnameOverride        = "fullnameOverride"
	NameOverride            = "nameOverride"
	KedaAutoscaling         = "kedaAutoscaling"
	HorizontalPodAutoscaler = "HorizontalPodAutoscaler"
	Enabled                 = "enabled"
	ReplicaCount            = "replicaCount"
	AppNameDevtronLabel     = "api.devtron.ai/appName"
	EnvNameDevtronLabel     = "api.devtron.ai/envName"
	ProjectNameDevtronLabel = "api.devtron.ai/project"
)

type GetMergedCmAndCsJsonV2Request struct {
	AppId                                 int
	EnvId                                 int
	PipeLineId                            int
	ChartVersion                          string
	DeploymentWithConfig                  bean.DeploymentConfigurationType
	WfrIdForDeploymentWithSpecificTrigger int
	Scope                                 resourceQualifiers.Scope
}

type MergedCmAndCsJsonV2Response struct {
	MergedJson     []byte
	ExternalCmList []string
	ExternalCsList []string
}

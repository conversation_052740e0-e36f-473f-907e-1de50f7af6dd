/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package repository

type PValUpdateKey string

const (
	EntityPValUpdateKey       PValUpdateKey = "Entity"
	EntityNamePValUpdateKey   PValUpdateKey = "EntityName"
	AppPValUpdateKey          PValUpdateKey = "App"
	EnvPValUpdateKey          PValUpdateKey = "Env"
	TeamPValUpdateKey         PValUpdateKey = "Team"
	ClusterPValUpdateKey      PValUpdateKey = "Cluster"
	NamespacePValUpdateKey    PValUpdateKey = "Namespace"
	GroupPValUpdateKey        PValUpdateKey = "Group"
	KindPValUpdateKey         PValUpdateKey = "Kind"
	ResourcePValUpdateKey     PValUpdateKey = "Resource"
	AppObjPValUpdateKey       PValUpdateKey = "AppObj"
	EnvObjPValUpdateKey       PValUpdateKey = "EnvObj"
	TeamObjPValUpdateKey      PValUpdateKey = "TeamObj"
	ClusterObjPValUpdateKey   PValUpdateKey = "ClusterObj"
	NamespaceObjPValUpdateKey PValUpdateKey = "NamespaceObj"
	GroupObjPValUpdateKey     PValUpdateKey = "GroupObj"
	KindObjPValUpdateKey      PValUpdateKey = "KindObj"
	ResourceObjPValUpdateKey  PValUpdateKey = "ResourceObj"
	WorkflowPValUpdateKey     PValUpdateKey = "Workflow"
	WorkflowObjPValUpdateKey  PValUpdateKey = "WorkflowObj"
)

const (
	EMPTY_PLACEHOLDER_FOR_QUERY = ""
)

// Code generated by mockery v2.42.0. DO NOT EDIT.

package mocks

import (
	commonService "github.com/devtron-labs/devtron/pkg/commonService"
	mock "github.com/stretchr/testify/mock"
)

// CommonService is an autogenerated mock type for the CommonService type
type CommonService struct {
	mock.Mock
}

// FetchLatestChartVersion provides a mock function with given fields: appId, envId
func (_m *CommonService) FetchLatestChartVersion(appId int, envId int) (string, error) {
	ret := _m.Called(appId, envId)

	if len(ret) == 0 {
		panic("no return value specified for FetchLatestChartVersion")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (string, error)); ok {
		return rf(appId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) string); ok {
		r0 = rf(appId, envId)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GlobalChecklist provides a mock function with given fields:
func (_m *CommonService) GlobalChecklist() (*commonService.GlobalChecklist, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GlobalChecklist")
	}

	var r0 *commonService.GlobalChecklist
	var r1 error
	if rf, ok := ret.Get(0).(func() (*commonService.GlobalChecklist, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *commonService.GlobalChecklist); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commonService.GlobalChecklist)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewCommonService creates a new instance of CommonService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCommonService(t interface {
	mock.TestingT
	Cleanup(func())
}) *CommonService {
	mock := &CommonService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

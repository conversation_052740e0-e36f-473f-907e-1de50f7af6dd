/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package generateManifest

import (
	"context"
	"errors"
	"github.com/devtron-labs/common-lib/utils/k8s"
	"github.com/devtron-labs/devtron/api/bean/AppView"
	client "github.com/devtron-labs/devtron/api/helm-app/gRPC"
	mocks4 "github.com/devtron-labs/devtron/api/helm-app/mocks"
	"github.com/devtron-labs/devtron/internal/sql/repository"
	mocks3 "github.com/devtron-labs/devtron/internal/sql/repository/mocks"
	"github.com/devtron-labs/devtron/internal/util"
	mocks6 "github.com/devtron-labs/devtron/internal/util/mocks"
	mocks2 "github.com/devtron-labs/devtron/pkg/app/mocks"
	"github.com/devtron-labs/devtron/pkg/chart"
	"github.com/devtron-labs/devtron/pkg/chart/bean"
	"github.com/devtron-labs/devtron/pkg/chart/mocks"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	mocks5 "github.com/devtron-labs/devtron/pkg/chartRepo/repository/mocks"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"testing"
)

var K8sUtilObj *k8s.K8sServiceImpl

func TestDeploymentTemplateServiceImpl_FetchDeploymentsWithChartRefs(t *testing.T) {
	defaultVersions := &chart.ChartRefResponse{
		ChartRefs: []chart.ChartRef{
			{
				Id:                    1,
				Version:               "v1.0.1",
				Name:                  "Deployment",
				Description:           "This is a deployment chart",
				UserUploaded:          false,
				IsAppMetricsSupported: false,
			},
			{
				Id:                    2,
				Version:               "v1.0.2",
				Name:                  "Deployment",
				Description:           "This is a deployment chart",
				UserUploaded:          false,
				IsAppMetricsSupported: false,
			},
			{
				Id:                    3,
				Version:               "v1.0.3",
				Name:                  "Deployment",
				Description:           "This is a deployment chart",
				UserUploaded:          false,
				IsAppMetricsSupported: false,
			},
		},
		LatestAppChartRef: 2,
		LatestEnvChartRef: 2,
	}
	publishedOnEnvs := []*AppView.Environment{
		{
			ChartRefId:      2,
			EnvironmentId:   1,
			EnvironmentName: "devtron-demo",
		},
	}

	deployedOnEnv := []*repository.DeploymentTemplateComparisonMetadata{
		{
			ChartRefId:               1,
			ChartVersion:             "4.18.1",
			EnvironmentId:            1,
			PipelineConfigOverrideId: 5,
			//StartedOn: 2023-08-26T16:36:55.732551Z,
			//FinishedOn: 2023-08-26T16:40:00.174576Z,
			Status: "Succeeded",
		},
		{
			ChartRefId:               1,
			ChartVersion:             "4.18.1",
			EnvironmentId:            1,
			PipelineConfigOverrideId: 5,
			//StartedOn: 2023-08-26T16:36:55.732551Z,
			//FinishedOn: 2023-08-26T16:40:00.174576Z,
			Status: "Succeeded",
		},
		{
			ChartRefId:               1,
			ChartVersion:             "4.18.1",
			EnvironmentId:            1,
			PipelineConfigOverrideId: 5,
			//StartedOn: 2023-08-26T16:36:55.732551Z,
			//FinishedOn: 2023-08-26T16:40:00.174576Z,
			Status: "Succeeded",
		},
	}

	deployedOnOtherEnvs := []*repository.DeploymentTemplateComparisonMetadata{
		{
			ChartRefId:               1,
			ChartVersion:             "4.18.1",
			EnvironmentId:            2,
			PipelineConfigOverrideId: 9,
		},
	}

	type args struct {
		appId int
		envId int
	}
	tests := []struct {
		name    string
		args    args
		want    []*repository.DeploymentTemplateComparisonMetadata
		wantErr error
	}{

		{
			name: "test for successfully fetching the list",
			args: args{
				appId: 1,
				envId: 1,
			},
			want: []*repository.DeploymentTemplateComparisonMetadata{
				{
					ChartRefId:               1,
					ChartVersion:             "v1.0.1",
					ChartType:                "Deployment",
					EnvironmentId:            0,
					EnvironmentName:          "",
					PipelineConfigOverrideId: 0,
					StartedOn:                nil,
					FinishedOn:               nil,
					Status:                   "",
					Type:                     1,
				},
				{
					ChartRefId:               2,
					ChartVersion:             "v1.0.2",
					ChartType:                "Deployment",
					EnvironmentId:            0,
					EnvironmentName:          "",
					PipelineConfigOverrideId: 0,
					StartedOn:                nil,
					FinishedOn:               nil,
					Status:                   "",
					Type:                     1,
				}, {
					ChartRefId:               3,
					ChartVersion:             "v1.0.3",
					ChartType:                "Deployment",
					EnvironmentId:            0,
					EnvironmentName:          "",
					PipelineConfigOverrideId: 0,
					StartedOn:                nil,
					FinishedOn:               nil,
					Status:                   "",
					Type:                     1,
				}, {
					ChartRefId:               2,
					ChartVersion:             "",
					ChartType:                "",
					EnvironmentId:            1,
					EnvironmentName:          "devtron-demo",
					PipelineConfigOverrideId: 0,
					StartedOn:                nil,
					FinishedOn:               nil,
					Status:                   "",
					Type:                     2,
				}, {
					ChartRefId:               1,
					ChartVersion:             "4.18.1",
					ChartType:                "",
					EnvironmentId:            1,
					EnvironmentName:          "",
					PipelineConfigOverrideId: 5,
					StartedOn:                nil,
					FinishedOn:               nil,
					Status:                   "Succeeded",
					Type:                     3,
				}, {
					ChartRefId:               1,
					ChartVersion:             "4.18.1",
					ChartType:                "",
					EnvironmentId:            1,
					EnvironmentName:          "",
					PipelineConfigOverrideId: 5,
					StartedOn:                nil,
					FinishedOn:               nil,
					Status:                   "Succeeded",
					Type:                     3,
				}, {
					ChartRefId:               1,
					ChartVersion:             "4.18.1",
					ChartType:                "",
					EnvironmentId:            1,
					EnvironmentName:          "",
					PipelineConfigOverrideId: 5,
					StartedOn:                nil,
					FinishedOn:               nil,
					Status:                   "Succeeded",
					Type:                     3,
				}, {
					ChartRefId:               1,
					ChartVersion:             "v1.0.1",
					ChartType:                "Deployment",
					EnvironmentId:            2,
					EnvironmentName:          "",
					PipelineConfigOverrideId: 9,
					StartedOn:                nil,
					FinishedOn:               nil,
					Status:                   "",
					Type:                     4,
				},
			},
		},
		{
			name: "test for error in chart",
			args: args{
				appId: 1,
				envId: 1,
			},
			wantErr: errors.New("error in getting defaultVersions"),
		},
		{
			name: "test for error in publishedOnEnvs",
			args: args{
				appId: 1,
				envId: 1,
			},
			wantErr: errors.New("error in getting publishedOnEnvs"),
		},
		{
			name: "test for error in deployedOnEnv",
			args: args{
				appId: 1,
				envId: 1,
			},
			wantErr: errors.New("error in getting deployedOnEnv"),
		},
		{
			name: "test for error in deployedOnOtherEnvs",
			args: args{
				appId: 1,
				envId: 1,
			},
			wantErr: errors.New("error in getting deployedOnOtherEnvs"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			impl, chartService, appListingService, deploymentTemplateRepository, _, _, _, _ := InitEventSimpleFactoryImpl(t)

			if tt.name == "test for successfully fetching the list" {
				chartService.On("ChartRefAutocompleteForAppOrEnv", tt.args.appId, 0).Return(defaultVersions, nil)
				appListingService.On("FetchMinDetailOtherEnvironment", tt.args.appId).Return(publishedOnEnvs, nil)
				deploymentTemplateRepository.On("FetchDeploymentHistoryWithChartRefs", tt.args.appId, tt.args.envId).Return(deployedOnEnv, nil)
				deploymentTemplateRepository.On("FetchLatestDeploymentWithChartRefs", tt.args.appId, tt.args.envId).Return(deployedOnOtherEnvs, nil)
			}

			if tt.name == "test for error in chart" {
				chartService.On("ChartRefAutocompleteForAppOrEnv", tt.args.appId, 0).Return(nil, errors.New("error in getting defaultVersions"))
			}

			if tt.name == "test for error in publishedOnEnvs" {
				chartService.On("ChartRefAutocompleteForAppOrEnv", tt.args.appId, 0).Return(defaultVersions, nil)
				appListingService.On("FetchMinDetailOtherEnvironment", tt.args.appId).Return(nil, errors.New("error in getting publishedOnEnvs"))
			}

			if tt.name == "test for error in deployedOnEnv" {
				chartService.On("ChartRefAutocompleteForAppOrEnv", tt.args.appId, 0).Return(defaultVersions, nil)
				appListingService.On("FetchMinDetailOtherEnvironment", tt.args.appId).Return(publishedOnEnvs, nil)
				deploymentTemplateRepository.On("FetchDeploymentHistoryWithChartRefs", tt.args.appId, tt.args.envId).Return(nil, errors.New("error in getting deployedOnEnv"))
			}

			if tt.name == "test for error in deployedOnOtherEnvs" {
				chartService.On("ChartRefAutocompleteForAppOrEnv", tt.args.appId, 0).Return(defaultVersions, nil)
				appListingService.On("FetchMinDetailOtherEnvironment", tt.args.appId).Return(publishedOnEnvs, nil)
				deploymentTemplateRepository.On("FetchDeploymentHistoryWithChartRefs", tt.args.appId, tt.args.envId).Return(deployedOnEnv, nil)
				deploymentTemplateRepository.On("FetchLatestDeploymentWithChartRefs", tt.args.appId, tt.args.envId).Return(deployedOnOtherEnvs, errors.New("error in getting deployedOnOtherEnvs"))
			}

			got, err := impl.FetchDeploymentsWithChartRefs(tt.args.appId, tt.args.envId)

			assert.Equal(t, err, tt.wantErr)

			assert.Equal(t, len(got), len(tt.want))
		})
	}
}

func TestDeploymentTemplateServiceImpl_GetDeploymentTemplate(t *testing.T) {

	var myMap = make(map[string]interface{})
	myString := "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"
	chart := &chartRepoRepository.Chart{}
	chart.GlobalOverride = "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"
	chart.Id = 1
	type args struct {
		ctx     context.Context
		request DeploymentTemplateRequest
	}
	tests := []struct {
		name string

		args    args
		want    DeploymentTemplateResponse
		wantErr error
	}{
		{
			name: "get values same as that of request",
			args: args{
				ctx: context.Background(),
				request: DeploymentTemplateRequest{
					Values:                "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}",
					ValuesAndManifestFlag: Values,
				},
			},
			want: DeploymentTemplateResponse{
				Data: "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"},
		},
		{
			name: "get values for base charts",
			args: args{
				ctx: context.Background(),
				request: DeploymentTemplateRequest{
					Values:                "",
					ValuesAndManifestFlag: Values,
					Type:                  1,
					ChartRefId:            1,
				},
			},
			want: DeploymentTemplateResponse{
				Data: "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"},
		},
		{
			name: "get values for published on other envs",
			args: args{
				ctx: context.Background(),
				request: DeploymentTemplateRequest{
					Values:                "",
					ValuesAndManifestFlag: Values,
					Type:                  2,
					ChartRefId:            1,
					AppId:                 1,
				},
			},
			want: DeploymentTemplateResponse{
				Data: "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"},
		},
		{
			name: "get error for published on other envs",
			args: args{
				ctx: context.Background(),
				request: DeploymentTemplateRequest{
					Values:                "",
					ValuesAndManifestFlag: Values,
					Type:                  2,
					ChartRefId:            1,
					AppId:                 1,
				},
			},
			wantErr: errors.New("error in getting chart"),
			want: DeploymentTemplateResponse{
				Data: "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"},
		},
		{
			name: "get values for deployed on envs",
			args: args{
				ctx: context.Background(),
				request: DeploymentTemplateRequest{
					Values:                   "",
					ValuesAndManifestFlag:    Values,
					Type:                     3,
					ChartRefId:               1,
					AppId:                    1,
					PipelineConfigOverrideId: 1,
				},
			},
			want: DeploymentTemplateResponse{
				Data: "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"},
		},
		{
			name: "get error for deployed on envs",
			args: args{
				ctx: context.Background(),
				request: DeploymentTemplateRequest{
					Values:                   "",
					ValuesAndManifestFlag:    Values,
					Type:                     3,
					ChartRefId:               1,
					AppId:                    1,
					PipelineConfigOverrideId: 1,
				},
			},
			wantErr: errors.New("error in getting values"),
			want: DeploymentTemplateResponse{
				Data: "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6969,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			impl, chartService, _, deploymentTemplateRepository, chartRepository, _, _, _ := InitEventSimpleFactoryImpl(t)
			if tt.name == "get values for base charts" {
				chartService.On("GetAppOverrideForDefaultTemplate", tt.args.request.ChartRefId).Return(myMap, myString, nil)
			}
			if tt.name == "get values for published on other envs" {
				chartRepository.On("FindLatestChartForAppByAppId", tt.args.request.AppId).Return(chart, nil)
			}

			if tt.name == "get error for published on other envs" {
				chartRepository.On("FindLatestChartForAppByAppId", tt.args.request.AppId).Return(nil, errors.New("error in getting chart"))
			}

			if tt.name == "get values for deployed on envs" {
				deploymentTemplateRepository.On("FetchPipelineOverrideValues", tt.args.request.PipelineConfigOverrideId).Return(myString, nil)
			}

			if tt.name == "get error for deployed on envs" {
				deploymentTemplateRepository.On("FetchPipelineOverrideValues", tt.args.request.PipelineConfigOverrideId).Return(myString, errors.New("error in getting values"))
			}

			got, err := impl.GetDeploymentTemplate(tt.args.ctx, tt.args.request)
			assert.Equal(t, tt.wantErr, err)
			if err == nil {
				assert.Equal(t, got.Data, tt.want.Data)
			}
		})
	}
}

func TestDeploymentTemplateServiceImpl_GetManifest(t *testing.T) {
	refChart := "refChart"
	template := "template"
	version := "version"
	myString := "myString"
	var chartBytes []byte

	t.Run("TestErrorInGettingRefChart", func(t *testing.T) {
		impl, chartService, _, _, _, _, _, _ := InitEventSimpleFactoryImpl(t)
		valuesYaml := "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6961,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"
		ctx := context.Background()
		request := bean.TemplateRequest{ChartRefId: 1}
		wantErr := errors.New("error in getting refChart")
		chartService.On("GetRefChart", request).Return(refChart, template, wantErr, version, myString)
		_, gotErr := impl.GetManifest(ctx, 1, valuesYaml)
		assert.Equal(t, gotErr, wantErr)
	})

	t.Run("TestManifestGeneration_Success", func(t *testing.T) {
		impl, chartService, _, _, _, chartTemplateServiceImpl, helmAppService, helmAppClient := InitEventSimpleFactoryImpl(t)
		valuesYaml := "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6962,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"
		ctx := context.Background()
		request := bean.TemplateRequest{ChartRefId: 2}
		var config *client.ClusterConfig
		templateChartResponse := &client.TemplateChartResponse{
			GeneratedManifest: "test generated manifest",
		}
		var zipPath string
		chartService.On("GetRefChart", request).Return(refChart, template, nil, version, myString)
		chartTemplateServiceImpl.On("LoadChartInBytes", "refChart", false, "", "").Return(chartBytes, zipPath, nil)
		helmAppService.On("GetClusterConf", 1).Return(config, nil)
		helmAppClient.On("TemplateChart", ctx, mock.AnythingOfType("*client.InstallReleaseRequest")).Return(templateChartResponse, nil)
		chartTemplateServiceImpl.On("CleanDir", zipPath)
		got, _ := impl.GetManifest(ctx, 2, valuesYaml)
		assert.Equal(t, *got.Manifest, templateChartResponse.GeneratedManifest)
	})

	t.Run("TestErrorInGetClusterConf", func(t *testing.T) {
		impl, chartService, _, _, _, chartTemplateServiceImpl, helmAppService, _ := InitEventSimpleFactoryImpl(t)
		valuesYaml := "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6963,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"
		ctx := context.Background()
		request := bean.TemplateRequest{ChartRefId: 5}
		wantErr1 := errors.New("error in fetching cluster detail")
		var zipPath string
		chartService.On("GetRefChart", request).Return("refChart5", "template5", nil, "version5", "myString5")
		chartTemplateServiceImpl.On("LoadChartInBytes", "refChart5", false, "", "").Return(chartBytes, zipPath, nil)
		helmAppService.On("GetClusterConf", 1).Return(nil, errors.New("error in fetching cluster detail"))
		chartTemplateServiceImpl.On("CleanDir", zipPath)
		_, gotErr1 := impl.GetManifest(ctx, 5, valuesYaml)
		assert.Equal(t, gotErr1, wantErr1)
	})

	t.Run("TestErrorInTemplateChart", func(t *testing.T) {
		impl, chartService, _, _, _, chartTemplateServiceImpl, helmAppService, helmAppClient := InitEventSimpleFactoryImpl(t)
		valuesYaml := "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6963,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"
		ctx := context.Background()
		request := bean.TemplateRequest{ChartRefId: 5}
		var config *client.ClusterConfig
		wantErr1 := errors.New("error in templating chart")
		var zipPath string
		chartService.On("GetRefChart", request).Return("refChart5", "template5", nil, "version5", "myString5")
		chartTemplateServiceImpl.On("LoadChartInBytes", "refChart5", false, "", "").Return(chartBytes, zipPath, nil)
		helmAppService.On("GetClusterConf", 1).Return(config, nil)
		helmAppClient.On("TemplateChart", ctx, mock.AnythingOfType("*client.InstallReleaseRequest")).Return(nil, errors.New("error in templating chart"))
		chartTemplateServiceImpl.On("CleanDir", zipPath)
		_, gotErr1 := impl.GetManifest(ctx, 5, valuesYaml)
		assert.Equal(t, gotErr1, wantErr1)
	})

	t.Run("TestErrorInLoadChartInBytes", func(t *testing.T) {
		impl, chartService, _, _, _, chartTemplateServiceImpl, _, _ := InitEventSimpleFactoryImpl(t)
		valuesYaml := "{\\\"ContainerPort\\\":[{\\\"envoyPort\\\":6964,\\\"idleTimeout\\\":\\\"6969s\\\",\\\"name\\\":\\\"app\\\",\\\"port\\\":6969,\\\"servicePort\\\":69,\\\"supportStreaming\\\":false,\\\"useHTTP2\\\":false}],\\\"EnvVariables\\\":[],\\\"GracePeriod\\\":30,\\\"LivenessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"MaxSurge\\\":1,\\\"MaxUnavailable\\\":0,\\\"MinReadySeconds\\\":60,\\\"ReadinessProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"scheme\\\":\\\"\\\",\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"Spec\\\":{\\\"Affinity\\\":{\\\"Key\\\":null,\\\"Values\\\":\\\"nodes\\\",\\\"key\\\":\\\"\\\"}},\\\"StartupProbe\\\":{\\\"Path\\\":\\\"\\\",\\\"command\\\":[],\\\"failureThreshold\\\":3,\\\"httpHeaders\\\":[],\\\"initialDelaySeconds\\\":20,\\\"periodSeconds\\\":10,\\\"port\\\":6969,\\\"successThreshold\\\":1,\\\"tcp\\\":false,\\\"timeoutSeconds\\\":5},\\\"ambassadorMapping\\\":{\\\"ambassadorId\\\":\\\"\\\",\\\"cors\\\":{},\\\"enabled\\\":false,\\\"hostname\\\":\\\"devtron.example.com\\\",\\\"labels\\\":{},\\\"prefix\\\":\\\"/\\\",\\\"retryPolicy\\\":{},\\\"rewrite\\\":\\\"\\\",\\\"tls\\\":{\\\"context\\\":\\\"\\\",\\\"create\\\":false,\\\"hosts\\\":[],\\\"secretName\\\":\\\"\\\"}},\\\"args\\\":{\\\"enabled\\\":false,\\\"value\\\":[\\\"/bin/sh\\\",\\\"-c\\\",\\\"touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600\\\"]},\\\"autoscaling\\\":{\\\"MaxReplicas\\\":2,\\\"MinReplicas\\\":1,\\\"TargetCPUUtilizationPercentage\\\":90,\\\"TargetMemoryUtilizationPercentage\\\":69,\\\"annotations\\\":{},\\\"behavior\\\":{},\\\"enabled\\\":false,\\\"extraMetrics\\\":[],\\\"labels\\\":{}},\\\"command\\\":{\\\"enabled\\\":false,\\\"value\\\":[],\\\"workingDir\\\":{}},\\\"containerSecurityContext\\\":{},\\\"containerSpec\\\":{\\\"lifecycle\\\":{\\\"enabled\\\":false,\\\"postStart\\\":{\\\"httpGet\\\":{\\\"host\\\":\\\"example.com\\\",\\\"path\\\":\\\"/example\\\",\\\"port\\\":90}},\\\"preStop\\\":{\\\"exec\\\":{\\\"command\\\":[\\\"sleep\\\",\\\"10\\\"]}}}},\\\"containers\\\":[],\\\"dbMigrationConfig\\\":{\\\"enabled\\\":false},\\\"envoyproxy\\\":{\\\"configMapName\\\":\\\"\\\",\\\"image\\\":\\\"docker.io/envoyproxy/envoy:v1.16.0\\\",\\\"lifecycle\\\":{},\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"50m\\\",\\\"memory\\\":\\\"50Mi\\\"}}},\\\"hostAliases\\\":[],\\\"image\\\":{\\\"pullPolicy\\\":\\\"IfNotPresent\\\"},\\\"imagePullSecrets\\\":[],\\\"ingress\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.local\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"labels\\\":{},\\\"tls\\\":[]},\\\"ingressInternal\\\":{\\\"annotations\\\":{},\\\"className\\\":\\\"\\\",\\\"enabled\\\":false,\\\"hosts\\\":[{\\\"host\\\":\\\"chart-example1.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example1\\\"]},{\\\"host\\\":\\\"chart-example2.internal\\\",\\\"pathType\\\":\\\"ImplementationSpecific\\\",\\\"paths\\\":[\\\"/example2\\\",\\\"/example2/healthz\\\"]}],\\\"tls\\\":[]},\\\"initContainers\\\":[],\\\"istio\\\":{\\\"authorizationPolicy\\\":{\\\"action\\\":null,\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"provider\\\":{},\\\"rules\\\":[]},\\\"destinationRule\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"subsets\\\":[],\\\"trafficPolicy\\\":{}},\\\"enable\\\":false,\\\"gateway\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"host\\\":\\\"example.com\\\",\\\"labels\\\":{},\\\"tls\\\":{\\\"enabled\\\":false,\\\"secretName\\\":\\\"secret-name\\\"}},\\\"peerAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"labels\\\":{},\\\"mtls\\\":{\\\"mode\\\":null},\\\"portLevelMtls\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"requestAuthentication\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"jwtRules\\\":[],\\\"labels\\\":{},\\\"selector\\\":{\\\"enabled\\\":false}},\\\"virtualService\\\":{\\\"annotations\\\":{},\\\"enabled\\\":false,\\\"gateways\\\":[],\\\"hosts\\\":[],\\\"http\\\":[],\\\"labels\\\":{}}},\\\"kedaAutoscaling\\\":{\\\"advanced\\\":{},\\\"authenticationRef\\\":{},\\\"enabled\\\":false,\\\"envSourceContainerName\\\":\\\"\\\",\\\"maxReplicaCount\\\":2,\\\"minReplicaCount\\\":1,\\\"triggerAuthentication\\\":{\\\"enabled\\\":false,\\\"name\\\":\\\"\\\",\\\"spec\\\":{}},\\\"triggers\\\":[]},\\\"networkPolicy\\\":{\\\"annotations\\\":{},\\\"egress\\\":[],\\\"enabled\\\":false,\\\"ingress\\\":[],\\\"labels\\\":{},\\\"podSelector\\\":{\\\"matchExpressions\\\":[],\\\"matchLabels\\\":{}},\\\"policyTypes\\\":[]},\\\"pauseForSecondsBeforeSwitchActive\\\":30,\\\"podAnnotations\\\":{},\\\"podDisruptionBudget\\\":{},\\\"podLabels\\\":{},\\\"podSecurityContext\\\":{},\\\"prometheus\\\":{\\\"release\\\":\\\"monitoring\\\"},\\\"rawYaml\\\":[],\\\"replicaCount\\\":1,\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":\\\"0.05\\\",\\\"memory\\\":\\\"50Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":\\\"0.01\\\",\\\"memory\\\":\\\"10Mi\\\"}},\\\"restartPolicy\\\":\\\"Always\\\",\\\"rolloutAnnotations\\\":{},\\\"rolloutLabels\\\":{},\\\"secret\\\":{\\\"data\\\":{},\\\"enabled\\\":false},\\\"server\\\":{\\\"deployment\\\":{\\\"image\\\":\\\"\\\",\\\"image_tag\\\":\\\"1-95af053\\\"}},\\\"service\\\":{\\\"annotations\\\":{},\\\"loadBalancerSourceRanges\\\":[],\\\"type\\\":\\\"ClusterIP\\\"},\\\"serviceAccount\\\":{\\\"annotations\\\":{},\\\"create\\\":false,\\\"name\\\":\\\"\\\"},\\\"servicemonitor\\\":{\\\"additionalLabels\\\":{}},\\\"tolerations\\\":[],\\\"topologySpreadConstraints\\\":[],\\\"volumeMounts\\\":[],\\\"volumes\\\":[],\\\"waitForSecondsBeforeScalingDown\\\":30,\\\"winterSoldier\\\":{\\\"action\\\":\\\"sleep\\\",\\\"annotation\\\":{},\\\"apiVersion\\\":\\\"pincher.devtron.ai/v1alpha1\\\",\\\"enabled\\\":false,\\\"fieldSelector\\\":[\\\"AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())\\\"],\\\"labels\\\":{},\\\"targetReplicas\\\":[],\\\"timeRangesWithZone\\\":{\\\"timeRanges\\\":[],\\\"timeZone\\\":\\\"Asia/Kolkata\\\"},\\\"type\\\":\\\"Rollout\\\"}}"
		ctx := context.Background()
		request := bean.TemplateRequest{ChartRefId: 3}
		wantErr := errors.New("error in getting chart")
		var zipPath string
		chartService.On("GetRefChart", request).Return("refChart1", "template1", nil, "version1", "myString1")
		chartTemplateServiceImpl.On("LoadChartInBytes", "refChart1", false, "", "").Return(chartBytes, zipPath, errors.New("error in getting chart"))
		_, err := impl.GetManifest(ctx, 3, valuesYaml)
		assert.Equal(t, err, wantErr)
	})
}

func InitEventSimpleFactoryImpl(t *testing.T) (*DeploymentTemplateServiceImpl, *mocks.ChartService, *mocks2.AppListingService, *mocks3.DeploymentTemplateRepository, *mocks5.ChartRepository, *mocks6.ChartTemplateService, *mocks4.HelmAppService, *mocks4.HelmAppClient) {
	logger, _ := util.NewSugardLogger()
	chartService := mocks.NewChartService(t)
	appListingService := mocks2.NewAppListingService(t)
	appListingRepository := mocks3.NewAppListingRepository(t)
	deploymentTemplateRepository := mocks3.NewDeploymentTemplateRepository(t)
	helmAppService := mocks4.NewHelmAppService(t)
	chartRepository := mocks5.NewChartRepository(t)
	chartTemplateServiceImpl := mocks6.NewChartTemplateService(t)
	helmAppClient := mocks4.NewHelmAppClient(t)
	var k8sUtil *k8s.K8sServiceImpl
	if K8sUtilObj != nil {
		k8sUtil = K8sUtilObj
	} else {
		config := &k8s.RuntimeConfig{LocalDevMode: true}
		k8sUtil = k8s.NewK8sUtil(logger, config)
		K8sUtilObj = k8sUtil
	}
	impl, _ := NewDeploymentTemplateServiceImpl(logger, chartService, appListingService, appListingRepository, deploymentTemplateRepository, helmAppService, chartRepository, chartTemplateServiceImpl, helmAppClient, k8sUtil, nil, nil, nil, nil, nil, nil, nil, nil)
	return impl, chartService, appListingService, deploymentTemplateRepository, chartRepository, chartTemplateServiceImpl, helmAppService, helmAppClient
}

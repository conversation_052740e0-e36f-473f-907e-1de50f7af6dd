// Code generated by mockery v2.42.0. DO NOT EDIT.

package mocks

import (
	"github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"
)

// EnvironmentRepository is an autogenerated mock type for the EnvironmentRepository type
type EnvironmentRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: mappings
func (_m *EnvironmentRepository) Create(mappings *repository.Environment) error {
	ret := _m.Called(mappings)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.Environment) error); ok {
		r0 = rf(mappings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindAll provides a mock function with given fields:
func (_m *EnvironmentRepository) FindAll() ([]repository.Environment, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAll")
	}

	var r0 []repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]repository.Environment, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []repository.Environment); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActive provides a mock function with given fields:
func (_m *EnvironmentRepository) FindAllActive() ([]*repository.Environment, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllActive")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*repository.Environment, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*repository.Environment); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveEnvOnlyDetails provides a mock function with given fields:
func (_m *EnvironmentRepository) FindAllActiveEnvOnlyDetails() ([]*repository.Environment, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllActiveEnvOnlyDetails")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*repository.Environment, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*repository.Environment); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveEnvironmentCount provides a mock function with given fields:
func (_m *EnvironmentRepository) FindAllActiveEnvironmentCount() (int, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllActiveEnvironmentCount")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func() (int, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveWithFilter provides a mock function with given fields:
func (_m *EnvironmentRepository) FindAllActiveWithFilter() ([]*repository.Environment, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllActiveWithFilter")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*repository.Environment, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*repository.Environment); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByClusterId provides a mock function with given fields: clusterId
func (_m *EnvironmentRepository) FindByClusterId(clusterId int) ([]*repository.Environment, error) {
	ret := _m.Called(clusterId)

	if len(ret) == 0 {
		panic("no return value specified for FindByClusterId")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*repository.Environment, error)); ok {
		return rf(clusterId)
	}
	if rf, ok := ret.Get(0).(func(int) []*repository.Environment); ok {
		r0 = rf(clusterId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(clusterId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByClusterIdAndNamespace provides a mock function with given fields: namespaceClusterPair
func (_m *EnvironmentRepository) FindByClusterIdAndNamespace(namespaceClusterPair []*repository.ClusterNamespacePair) ([]*repository.Environment, error) {
	ret := _m.Called(namespaceClusterPair)

	if len(ret) == 0 {
		panic("no return value specified for FindByClusterIdAndNamespace")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func([]*repository.ClusterNamespacePair) ([]*repository.Environment, error)); ok {
		return rf(namespaceClusterPair)
	}
	if rf, ok := ret.Get(0).(func([]*repository.ClusterNamespacePair) []*repository.Environment); ok {
		r0 = rf(namespaceClusterPair)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func([]*repository.ClusterNamespacePair) error); ok {
		r1 = rf(namespaceClusterPair)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByClusterIds provides a mock function with given fields: clusterIds
func (_m *EnvironmentRepository) FindByClusterIds(clusterIds []int) ([]*repository.Environment, error) {
	ret := _m.Called(clusterIds)

	if len(ret) == 0 {
		panic("no return value specified for FindByClusterIds")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*repository.Environment, error)); ok {
		return rf(clusterIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*repository.Environment); ok {
		r0 = rf(clusterIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(clusterIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByClusterIdsWithFilter provides a mock function with given fields: clusterIds
func (_m *EnvironmentRepository) FindByClusterIdsWithFilter(clusterIds []int) ([]*repository.Environment, error) {
	ret := _m.Called(clusterIds)

	if len(ret) == 0 {
		panic("no return value specified for FindByClusterIdsWithFilter")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*repository.Environment, error)); ok {
		return rf(clusterIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*repository.Environment); ok {
		r0 = rf(clusterIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(clusterIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByEnvName provides a mock function with given fields: envName
func (_m *EnvironmentRepository) FindByEnvName(envName string) ([]*repository.Environment, error) {
	ret := _m.Called(envName)

	if len(ret) == 0 {
		panic("no return value specified for FindByEnvName")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]*repository.Environment, error)); ok {
		return rf(envName)
	}
	if rf, ok := ret.Get(0).(func(string) []*repository.Environment); ok {
		r0 = rf(envName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(envName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByEnvNameAndClusterIds provides a mock function with given fields: envName, clusterIds
func (_m *EnvironmentRepository) FindByEnvNameAndClusterIds(envName string, clusterIds []int) ([]*repository.Environment, error) {
	ret := _m.Called(envName, clusterIds)

	if len(ret) == 0 {
		panic("no return value specified for FindByEnvNameAndClusterIds")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string, []int) ([]*repository.Environment, error)); ok {
		return rf(envName, clusterIds)
	}
	if rf, ok := ret.Get(0).(func(string, []int) []*repository.Environment); ok {
		r0 = rf(envName, clusterIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string, []int) error); ok {
		r1 = rf(envName, clusterIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByEnvNameOrIdentifierOrNamespace provides a mock function with given fields: clusterId, envName, identifier, namespace
func (_m *EnvironmentRepository) FindByEnvNameOrIdentifierOrNamespace(clusterId int, envName string, identifier string, namespace string) (*repository.Environment, error) {
	ret := _m.Called(clusterId, envName, identifier, namespace)

	if len(ret) == 0 {
		panic("no return value specified for FindByEnvNameOrIdentifierOrNamespace")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(int, string, string, string) (*repository.Environment, error)); ok {
		return rf(clusterId, envName, identifier, namespace)
	}
	if rf, ok := ret.Get(0).(func(int, string, string, string) *repository.Environment); ok {
		r0 = rf(clusterId, envName, identifier, namespace)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(int, string, string, string) error); ok {
		r1 = rf(clusterId, envName, identifier, namespace)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: id
func (_m *EnvironmentRepository) FindById(id int) (*repository.Environment, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindById")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*repository.Environment, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *repository.Environment); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdentifier provides a mock function with given fields: identifier
func (_m *EnvironmentRepository) FindByIdentifier(identifier string) (*repository.Environment, error) {
	ret := _m.Called(identifier)

	if len(ret) == 0 {
		panic("no return value specified for FindByIdentifier")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*repository.Environment, error)); ok {
		return rf(identifier)
	}
	if rf, ok := ret.Get(0).(func(string) *repository.Environment); ok {
		r0 = rf(identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(identifier)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIds provides a mock function with given fields: ids
func (_m *EnvironmentRepository) FindByIds(ids []*int) ([]*repository.Environment, error) {
	ret := _m.Called(ids)

	if len(ret) == 0 {
		panic("no return value specified for FindByIds")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func([]*int) ([]*repository.Environment, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]*int) []*repository.Environment); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func([]*int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByName provides a mock function with given fields: name
func (_m *EnvironmentRepository) FindByName(name string) (*repository.Environment, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for FindByName")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*repository.Environment, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) *repository.Environment); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByNameOrIdentifier provides a mock function with given fields: name, identifier
func (_m *EnvironmentRepository) FindByNameOrIdentifier(name string, identifier string) (*repository.Environment, error) {
	ret := _m.Called(name, identifier)

	if len(ret) == 0 {
		panic("no return value specified for FindByNameOrIdentifier")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*repository.Environment, error)); ok {
		return rf(name, identifier)
	}
	if rf, ok := ret.Get(0).(func(string, string) *repository.Environment); ok {
		r0 = rf(name, identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(name, identifier)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByNames provides a mock function with given fields: envNames
func (_m *EnvironmentRepository) FindByNames(envNames []string) ([]*repository.Environment, error) {
	ret := _m.Called(envNames)

	if len(ret) == 0 {
		panic("no return value specified for FindByNames")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) ([]*repository.Environment, error)); ok {
		return rf(envNames)
	}
	if rf, ok := ret.Get(0).(func([]string) []*repository.Environment); ok {
		r0 = rf(envNames)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(envNames)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByNamespaceAndClusterName provides a mock function with given fields: namespaces, clusterName
func (_m *EnvironmentRepository) FindByNamespaceAndClusterName(namespaces string, clusterName string) (*repository.Environment, error) {
	ret := _m.Called(namespaces, clusterName)

	if len(ret) == 0 {
		panic("no return value specified for FindByNamespaceAndClusterName")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*repository.Environment, error)); ok {
		return rf(namespaces, clusterName)
	}
	if rf, ok := ret.Get(0).(func(string, string) *repository.Environment); ok {
		r0 = rf(namespaces, clusterName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(namespaces, clusterName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindEnvByNameWithClusterDetails provides a mock function with given fields: envName
func (_m *EnvironmentRepository) FindEnvByNameWithClusterDetails(envName string) (*repository.Environment, error) {
	ret := _m.Called(envName)

	if len(ret) == 0 {
		panic("no return value specified for FindEnvByNameWithClusterDetails")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*repository.Environment, error)); ok {
		return rf(envName)
	}
	if rf, ok := ret.Get(0).(func(string) *repository.Environment); ok {
		r0 = rf(envName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(envName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindEnvClusterInfosByIds provides a mock function with given fields: _a0
func (_m *EnvironmentRepository) FindEnvClusterInfosByIds(_a0 []int) ([]*repository.EnvCluserInfo, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for FindEnvClusterInfosByIds")
	}

	var r0 []*repository.EnvCluserInfo
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*repository.EnvCluserInfo, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func([]int) []*repository.EnvCluserInfo); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.EnvCluserInfo)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindEnvLinkedWithCiPipelines provides a mock function with given fields: externalCi, ciPipelineIds
func (_m *EnvironmentRepository) FindEnvLinkedWithCiPipelines(externalCi bool, ciPipelineIds []int) ([]*repository.Environment, error) {
	ret := _m.Called(externalCi, ciPipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FindEnvLinkedWithCiPipelines")
	}

	var r0 []*repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(bool, []int) ([]*repository.Environment, error)); ok {
		return rf(externalCi, ciPipelineIds)
	}
	if rf, ok := ret.Get(0).(func(bool, []int) []*repository.Environment); ok {
		r0 = rf(externalCi, ciPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(bool, []int) error); ok {
		r1 = rf(externalCi, ciPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindIdByName provides a mock function with given fields: name
func (_m *EnvironmentRepository) FindIdByName(name string) (int, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for FindIdByName")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (int, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) int); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindIdsByNames provides a mock function with given fields: envNames
func (_m *EnvironmentRepository) FindIdsByNames(envNames []string) ([]int, error) {
	ret := _m.Called(envNames)

	if len(ret) == 0 {
		panic("no return value specified for FindIdsByNames")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) ([]int, error)); ok {
		return rf(envNames)
	}
	if rf, ok := ret.Get(0).(func([]string) []int); ok {
		r0 = rf(envNames)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(envNames)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOne provides a mock function with given fields: environment
func (_m *EnvironmentRepository) FindOne(environment string) (*repository.Environment, error) {
	ret := _m.Called(environment)

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*repository.Environment, error)); ok {
		return rf(environment)
	}
	if rf, ok := ret.Get(0).(func(string) *repository.Environment); ok {
		r0 = rf(environment)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(environment)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOneByNamespaceAndClusterId provides a mock function with given fields: namespace, clusterId
func (_m *EnvironmentRepository) FindOneByNamespaceAndClusterId(namespace string, clusterId int) (*repository.Environment, error) {
	ret := _m.Called(namespace, clusterId)

	if len(ret) == 0 {
		panic("no return value specified for FindOneByNamespaceAndClusterId")
	}

	var r0 *repository.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(string, int) (*repository.Environment, error)); ok {
		return rf(namespace, clusterId)
	}
	if rf, ok := ret.Get(0).(func(string, int) *repository.Environment); ok {
		r0 = rf(namespace, clusterId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(string, int) error); ok {
		r1 = rf(namespace, clusterId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConnection provides a mock function with given fields:
func (_m *EnvironmentRepository) GetConnection() *pg.DB {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetConnection")
	}

	var r0 *pg.DB
	if rf, ok := ret.Get(0).(func() *pg.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.DB)
		}
	}

	return r0
}

// MarkEnvironmentDeleted provides a mock function with given fields: mappings, tx
func (_m *EnvironmentRepository) MarkEnvironmentDeleted(mappings *repository.Environment, tx *pg.Tx) error {
	ret := _m.Called(mappings, tx)

	if len(ret) == 0 {
		panic("no return value specified for MarkEnvironmentDeleted")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.Environment, *pg.Tx) error); ok {
		r0 = rf(mappings, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: mappings
func (_m *EnvironmentRepository) Update(mappings *repository.Environment) error {
	ret := _m.Called(mappings)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.Environment) error); ok {
		r0 = rf(mappings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewEnvironmentRepository creates a new instance of EnvironmentRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewEnvironmentRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *EnvironmentRepository {
	mock := &EnvironmentRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package deployment

import (
	"context"
	"errors"
	bean2 "github.com/devtron-labs/devtron/api/helm-app/bean"
	"github.com/devtron-labs/devtron/api/helm-app/gRPC"
	client "github.com/devtron-labs/devtron/api/helm-app/service"
	helmBean "github.com/devtron-labs/devtron/api/helm-app/service/bean"
	"github.com/devtron-labs/devtron/api/helm-app/service/read"
	repository2 "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/timelineStatus"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/bean"
	appStoreDeploymentCommon "github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/common"
	util2 "github.com/devtron-labs/devtron/pkg/appStore/util"
	commonBean "github.com/devtron-labs/devtron/pkg/deployment/gitOps/common/bean"
	validationBean "github.com/devtron-labs/devtron/pkg/deployment/gitOps/validation/bean"
	clientErrors "github.com/devtron-labs/devtron/pkg/errors"
	"net/http"
	"time"

	openapi "github.com/devtron-labs/devtron/api/helm-app/openapiClient"
	"github.com/devtron-labs/devtron/internal/util"
	appStoreBean "github.com/devtron-labs/devtron/pkg/appStore/bean"
	appStoreDiscoverRepository "github.com/devtron-labs/devtron/pkg/appStore/discover/repository"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/repository"
	"github.com/go-pg/pg"
	"go.opentelemetry.io/otel"
	"go.uber.org/zap"
	"sigs.k8s.io/yaml"
)

type EAModeDeploymentService interface {
	InstallApp(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, chartGitAttr *commonBean.ChartGitAttribute, ctx context.Context, tx *pg.Tx) (*appStoreBean.InstallAppVersionDTO, error)
	DeleteInstalledApp(ctx context.Context, appName string, environmentName string, installAppVersionRequest *appStoreBean.InstallAppVersionDTO, installedApps *repository.InstalledApps, dbTransaction *pg.Tx) error
	RollbackRelease(ctx context.Context, installedApp *appStoreBean.InstallAppVersionDTO, deploymentVersion int32) (*appStoreBean.InstallAppVersionDTO, bool, error)
	GetDeploymentHistory(ctx context.Context, installedApp *appStoreBean.InstallAppVersionDTO) (*gRPC.HelmAppDeploymentHistory, error)
	GetDeploymentHistoryInfo(ctx context.Context, installedApp *appStoreBean.InstallAppVersionDTO, version int32) (*openapi.HelmAppDeploymentManifestDetail, error)
	UpgradeDeployment(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, ChartGitAttribute *commonBean.ChartGitAttribute, installedAppVersionHistoryId int, ctx context.Context) error
}

type EAModeDeploymentServiceImpl struct {
	Logger                               *zap.SugaredLogger
	helmAppService                       client.HelmAppService
	helmAppReadService                   read.HelmAppReadService
	appStoreApplicationVersionRepository appStoreDiscoverRepository.AppStoreApplicationVersionRepository
	appStoreDeploymentCommonService      appStoreDeploymentCommon.AppStoreDeploymentCommonService
	// TODO fix me next
	helmAppClient               gRPC.HelmAppClient // TODO refactoring: use HelmAppService instead
	installedAppRepository      repository.InstalledAppRepository
	OCIRegistryConfigRepository repository2.OCIRegistryConfigRepository
}

func NewEAModeDeploymentServiceImpl(logger *zap.SugaredLogger, helmAppService client.HelmAppService,
	appStoreApplicationVersionRepository appStoreDiscoverRepository.AppStoreApplicationVersionRepository,
	helmAppClient gRPC.HelmAppClient,
	installedAppRepository repository.InstalledAppRepository,
	OCIRegistryConfigRepository repository2.OCIRegistryConfigRepository,
	appStoreDeploymentCommonService appStoreDeploymentCommon.AppStoreDeploymentCommonService,
	helmAppReadService read.HelmAppReadService,
) *EAModeDeploymentServiceImpl {
	return &EAModeDeploymentServiceImpl{
		Logger:                               logger,
		helmAppService:                       helmAppService,
		helmAppReadService:                   helmAppReadService,
		appStoreApplicationVersionRepository: appStoreApplicationVersionRepository,
		helmAppClient:                        helmAppClient,
		installedAppRepository:               installedAppRepository,
		OCIRegistryConfigRepository:          OCIRegistryConfigRepository,
		appStoreDeploymentCommonService:      appStoreDeploymentCommonService,
	}
}

func (impl *EAModeDeploymentServiceImpl) UpgradeDeployment(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, ChartGitAttribute *commonBean.ChartGitAttribute, installedAppVersionHistoryId int, ctx context.Context) error {
	err := impl.updateApplicationWithChartInfo(ctx, installAppVersionRequest.InstalledAppId, installAppVersionRequest.AppStoreVersion, installAppVersionRequest.ValuesOverrideYaml, installedAppVersionHistoryId)
	if err != nil {
		impl.Logger.Errorw("error in updating helm app", "err", err)
		return err
	}
	return nil
}

func (impl *EAModeDeploymentServiceImpl) InstallApp(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, chartGitAttr *commonBean.ChartGitAttribute, ctx context.Context, tx *pg.Tx) (*appStoreBean.InstallAppVersionDTO, error) {
	installAppVersionRequest.UpdateDeploymentAppType(util.PIPELINE_DEPLOYMENT_TYPE_HELM)
	appStoreAppVersion, err := impl.appStoreApplicationVersionRepository.FindById(installAppVersionRequest.AppStoreVersion)
	if err != nil {
		impl.Logger.Errorw("fetching error", "err", err)
		return installAppVersionRequest, err
	}
	var IsOCIRepo bool
	var registryCredential *gRPC.RegistryCredential
	var chartRepository *gRPC.ChartRepository
	dockerRegistryId := appStoreAppVersion.AppStore.DockerArtifactStoreId
	if dockerRegistryId != "" {
		ociRegistryConfigs, err := impl.OCIRegistryConfigRepository.FindByDockerRegistryId(dockerRegistryId)
		if err != nil {
			impl.Logger.Errorw("error in fetching oci registry config", "err", err)
			return nil, err
		}
		var ociRegistryConfig *repository2.OCIRegistryConfig
		for _, config := range ociRegistryConfigs {
			if config.RepositoryAction == repository2.STORAGE_ACTION_TYPE_PULL || config.RepositoryAction == repository2.STORAGE_ACTION_TYPE_PULL_AND_PUSH {
				ociRegistryConfig = config
				break
			}
		}
		IsOCIRepo = true
		registryCredential = &gRPC.RegistryCredential{
			RegistryUrl:         appStoreAppVersion.AppStore.DockerArtifactStore.RegistryURL,
			Username:            appStoreAppVersion.AppStore.DockerArtifactStore.Username,
			Password:            appStoreAppVersion.AppStore.DockerArtifactStore.Password,
			AwsRegion:           appStoreAppVersion.AppStore.DockerArtifactStore.AWSRegion,
			AccessKey:           appStoreAppVersion.AppStore.DockerArtifactStore.AWSAccessKeyId,
			SecretKey:           appStoreAppVersion.AppStore.DockerArtifactStore.AWSSecretAccessKey,
			RegistryType:        string(appStoreAppVersion.AppStore.DockerArtifactStore.RegistryType),
			RepoName:            appStoreAppVersion.AppStore.Name,
			IsPublic:            ociRegistryConfig.IsPublic,
			Connection:          appStoreAppVersion.AppStore.DockerArtifactStore.Connection,
			RegistryName:        appStoreAppVersion.AppStore.DockerArtifactStoreId,
			RegistryCertificate: appStoreAppVersion.AppStore.DockerArtifactStore.Cert,
		}
	} else {
		chartRepository = &gRPC.ChartRepository{
			Name:                    appStoreAppVersion.AppStore.ChartRepo.Name,
			Url:                     appStoreAppVersion.AppStore.ChartRepo.Url,
			Username:                appStoreAppVersion.AppStore.ChartRepo.UserName,
			Password:                appStoreAppVersion.AppStore.ChartRepo.Password,
			AllowInsecureConnection: appStoreAppVersion.AppStore.ChartRepo.AllowInsecureConnection,
		}
	}
	installReleaseRequest := &gRPC.InstallReleaseRequest{
		ChartName:       appStoreAppVersion.Name,
		ChartVersion:    appStoreAppVersion.Version,
		ValuesYaml:      installAppVersionRequest.ValuesOverrideYaml,
		ChartRepository: chartRepository,
		ReleaseIdentifier: &gRPC.ReleaseIdentifier{
			ReleaseNamespace: installAppVersionRequest.Namespace,
			ReleaseName:      installAppVersionRequest.AppName,
		},
		IsOCIRepo:                  IsOCIRepo,
		RegistryCredential:         registryCredential,
		InstallAppVersionHistoryId: int32(installAppVersionRequest.InstalledAppVersionHistoryId),
	}

	_, err = impl.helmAppService.InstallRelease(ctx, installAppVersionRequest.ClusterId, installReleaseRequest)
	if err != nil {
		apiError := clientErrors.ConvertToApiError(err)
		if apiError != nil {
			err = apiError
		}
		return installAppVersionRequest, err
	}
	return installAppVersionRequest, nil
}

func (impl *EAModeDeploymentServiceImpl) DeleteInstalledApp(ctx context.Context, appName string, environmentName string, installAppVersionRequest *appStoreBean.InstallAppVersionDTO, installedApps *repository.InstalledApps, dbTransaction *pg.Tx) error {
	if installAppVersionRequest.ForceDelete {
		return nil
	}
	appIdentifier := &helmBean.AppIdentifier{
		ClusterId:   installAppVersionRequest.ClusterId,
		ReleaseName: installAppVersionRequest.AppName,
		Namespace:   installAppVersionRequest.Namespace,
	}

	isInstalled, err := impl.helmAppService.IsReleaseInstalled(ctx, appIdentifier)
	if err != nil {
		if client.IsClusterUnReachableError(err) {
			impl.Logger.Errorw("k8s cluster unreachable", "err", err)
			return &util.ApiError{HttpStatusCode: http.StatusUnprocessableEntity, UserMessage: err.Error()}
		}
		impl.Logger.Errorw("error in checking if helm release is installed or not", "error", err, "appIdentifier", appIdentifier)
		return err
	}

	if isInstalled {
		deleteResponse, err := impl.helmAppService.DeleteApplication(ctx, appIdentifier)
		if err != nil {
			impl.Logger.Errorw("error in deleting helm application", "error", err, "appIdentifier", appIdentifier)
			apiError := clientErrors.ConvertToApiError(err)
			if apiError != nil {
				err = apiError
			}
			return err
		}
		if deleteResponse == nil || !deleteResponse.GetSuccess() {
			return errors.New("delete application response unsuccessful")
		}
	}

	return nil
}

// returns - valuesYamlStr, success, error
func (impl *EAModeDeploymentServiceImpl) RollbackRelease(ctx context.Context, installedApp *appStoreBean.InstallAppVersionDTO, deploymentVersion int32) (*appStoreBean.InstallAppVersionDTO, bool, error) {

	//this flow is dedicated for cli helm apps i.e. externally installed apps, so there is no need to perform any db operation, the version request here was from directly helm.
	helmAppIdentifier := &helmBean.AppIdentifier{
		ClusterId:   installedApp.ClusterId,
		Namespace:   installedApp.Namespace,
		ReleaseName: installedApp.AppName,
	}

	helmAppDeploymentDetail, err := impl.helmAppService.GetDeploymentDetail(ctx, helmAppIdentifier, deploymentVersion)
	if err != nil {
		impl.Logger.Errorw("Error in getting helm application deployment detail", "err", err)
		apiError := clientErrors.ConvertToApiError(err)
		if apiError != nil {
			err = apiError
		}
		return installedApp, false, err
	}
	valuesYamlJson := helmAppDeploymentDetail.GetValuesYaml()
	valuesYamlByteArr, err := yaml.JSONToYAML([]byte(valuesYamlJson))
	if err != nil {
		impl.Logger.Errorw("Error in converting json to yaml", "err", err)
		return installedApp, false, err
	}

	// send to helm
	success, err := impl.helmAppService.RollbackRelease(ctx, helmAppIdentifier, deploymentVersion)
	if err != nil {
		impl.Logger.Errorw("Error in helm rollback release", "err", err)
		return installedApp, false, err
	}
	installedApp.ValuesOverrideYaml = string(valuesYamlByteArr)
	return installedApp, success, nil
}

func (impl *EAModeDeploymentServiceImpl) GetDeploymentHistory(ctx context.Context, installedApp *appStoreBean.InstallAppVersionDTO) (*gRPC.HelmAppDeploymentHistory, error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "EAModeDeploymentServiceImpl.GetDeploymentHistory")
	defer span.End()
	if installedApp.InstalledAppId > 0 {
		history, err := impl.appStoreDeploymentCommonService.GetDeploymentHistoryFromDB(ctx, installedApp)
		if err != nil {
			impl.Logger.Errorw("error while fetching deployment history", "error", err)
			return history, err
		}
		return history, nil
	} else {
		helmAppIdentifier := &helmBean.AppIdentifier{
			ClusterId:   installedApp.ClusterId,
			Namespace:   installedApp.Namespace,
			ReleaseName: installedApp.AppName,
		}
		history, err := impl.helmAppService.GetDeploymentHistory(newCtx, helmAppIdentifier)
		if err != nil {
			apiError := clientErrors.ConvertToApiError(err)
			if apiError != nil {
				err = apiError
			}
		}
		return history, err
	}

}
func (impl *EAModeDeploymentServiceImpl) GetDeploymentHistoryInfo(ctx context.Context, installedApp *appStoreBean.InstallAppVersionDTO, version int32) (*openapi.HelmAppDeploymentManifestDetail, error) {
	if installedApp.InstalledAppId > 0 {
		res, err := impl.appStoreDeploymentCommonService.GetDeploymentHistoryInfoFromDB(ctx, installedApp, version)
		if err != nil {
			impl.Logger.Errorw("error while fetching installed version history", "error", err)
			return nil, err
		}
		return res, nil
	} else {
		helmAppIdentifier := &helmBean.AppIdentifier{
			ClusterId:   installedApp.ClusterId,
			Namespace:   installedApp.Namespace,
			ReleaseName: installedApp.AppName,
		}
		config, err := impl.helmAppReadService.GetClusterConf(helmAppIdentifier.ClusterId)
		if err != nil {
			impl.Logger.Errorw("error in fetching cluster detail", "clusterId", helmAppIdentifier.ClusterId, "err", err)
			return nil, err
		}

		req := &gRPC.DeploymentDetailRequest{
			ReleaseIdentifier: &gRPC.ReleaseIdentifier{
				ClusterConfig:    config,
				ReleaseName:      helmAppIdentifier.ReleaseName,
				ReleaseNamespace: helmAppIdentifier.Namespace,
			},
			DeploymentVersion: version,
		}
		_, span := otel.Tracer("orchestrator").Start(ctx, "helmAppClient.GetDeploymentDetail")
		deploymentDetail, err := impl.helmAppClient.GetDeploymentDetail(ctx, req)
		span.End()
		if err != nil {
			impl.Logger.Errorw("error in getting deployment detail", "err", err)
			apiError := clientErrors.ConvertToApiError(err)
			if apiError != nil {
				err = apiError
			}
			return nil, err
		}

		response := &openapi.HelmAppDeploymentManifestDetail{
			Manifest:   &deploymentDetail.Manifest,
			ValuesYaml: &deploymentDetail.ValuesYaml,
		}

		return response, nil
	}

}

func (impl *EAModeDeploymentServiceImpl) updateApplicationWithChartInfo(ctx context.Context, installedAppId int, appStoreApplicationVersionId int, valuesOverrideYaml string, installAppVersionHistoryId int) error {
	installedApp, err := impl.installedAppRepository.GetInstalledApp(installedAppId)
	if err != nil {
		impl.Logger.Errorw("error in getting in installedApp", "installedAppId", installedAppId, "err", err)
		return err
	}
	appName := installedApp.App.AppName
	if util2.IsExternalChartStoreApp(installedApp.App.DisplayName) {
		appName = installedApp.App.DisplayName
	}
	appStoreApplicationVersion, err := impl.appStoreApplicationVersionRepository.FindById(appStoreApplicationVersionId)
	if err != nil {
		impl.Logger.Errorw("error in getting in appStoreApplicationVersion", "appStoreApplicationVersionId", appStoreApplicationVersionId, "err", err)
		return err
	}
	var IsOCIRepo bool
	var registryCredential *gRPC.RegistryCredential
	var chartRepository *gRPC.ChartRepository
	dockerRegistryId := appStoreApplicationVersion.AppStore.DockerArtifactStoreId
	if dockerRegistryId != "" {
		ociRegistryConfigs, err := impl.OCIRegistryConfigRepository.FindByDockerRegistryId(dockerRegistryId)
		if err != nil {
			impl.Logger.Errorw("error in fetching oci registry config", "err", err)
			return err
		}
		var ociRegistryConfig *repository2.OCIRegistryConfig
		for _, config := range ociRegistryConfigs {
			if config.RepositoryAction == repository2.STORAGE_ACTION_TYPE_PULL || config.RepositoryAction == repository2.STORAGE_ACTION_TYPE_PULL_AND_PUSH {
				ociRegistryConfig = config
				break
			}
		}
		IsOCIRepo = true
		registryCredential = &gRPC.RegistryCredential{
			RegistryUrl:         appStoreApplicationVersion.AppStore.DockerArtifactStore.RegistryURL,
			Username:            appStoreApplicationVersion.AppStore.DockerArtifactStore.Username,
			Password:            appStoreApplicationVersion.AppStore.DockerArtifactStore.Password,
			AwsRegion:           appStoreApplicationVersion.AppStore.DockerArtifactStore.AWSRegion,
			AccessKey:           appStoreApplicationVersion.AppStore.DockerArtifactStore.AWSAccessKeyId,
			SecretKey:           appStoreApplicationVersion.AppStore.DockerArtifactStore.AWSSecretAccessKey,
			RegistryType:        string(appStoreApplicationVersion.AppStore.DockerArtifactStore.RegistryType),
			RepoName:            appStoreApplicationVersion.AppStore.Name,
			IsPublic:            ociRegistryConfig.IsPublic,
			Connection:          appStoreApplicationVersion.AppStore.DockerArtifactStore.Connection,
			RegistryName:        appStoreApplicationVersion.AppStore.DockerArtifactStoreId,
			RegistryCertificate: appStoreApplicationVersion.AppStore.DockerArtifactStore.Cert,
		}
	} else {
		chartRepository = &gRPC.ChartRepository{
			Name:                    appStoreApplicationVersion.AppStore.ChartRepo.Name,
			Url:                     appStoreApplicationVersion.AppStore.ChartRepo.Url,
			Username:                appStoreApplicationVersion.AppStore.ChartRepo.UserName,
			Password:                appStoreApplicationVersion.AppStore.ChartRepo.Password,
			AllowInsecureConnection: appStoreApplicationVersion.AppStore.ChartRepo.AllowInsecureConnection,
		}
	}

	updateReleaseRequest := &bean2.UpdateApplicationWithChartInfoRequestDto{
		InstallReleaseRequest: &gRPC.InstallReleaseRequest{
			ValuesYaml: valuesOverrideYaml,
			ReleaseIdentifier: &gRPC.ReleaseIdentifier{
				ReleaseNamespace: installedApp.Environment.Namespace,
				ReleaseName:      appName,
			},
			ChartName:                  appStoreApplicationVersion.Name,
			ChartVersion:               appStoreApplicationVersion.Version,
			ChartRepository:            chartRepository,
			RegistryCredential:         registryCredential,
			IsOCIRepo:                  IsOCIRepo,
			InstallAppVersionHistoryId: int32(installAppVersionHistoryId),
		},
		SourceAppType: bean2.SOURCE_HELM_APP,
	}
	res, err := impl.helmAppService.UpdateApplicationWithChartInfo(ctx, installedApp.Environment.ClusterId, updateReleaseRequest)
	if err != nil {
		impl.Logger.Errorw("error in updating helm application", "err", err)
		return err
	}
	if !res.GetSuccess() {
		return errors.New("helm application update unsuccessful")
	}
	return nil
}

func (impl *EAModeDeploymentServiceImpl) GetAcdAppGitOpsRepoName(appName string, environmentName string) (string, error) {
	return "", errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) DeleteACDAppObject(ctx context.Context, appName string, environmentName string, installAppVersionRequest *appStoreBean.InstallAppVersionDTO) error {
	return errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) SaveTimelineForHelmApps(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, status timelineStatus.TimelineStatus, statusDetail string, statusTime time.Time, tx *pg.Tx) error {
	return errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) UpdateInstalledAppAndPipelineStatusForFailedDeploymentStatus(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, triggeredAt time.Time, err error) error {
	return errors.New("this is not implemented")
}

// TODO: Need to refactor this,refer below reason
// This is being done as in ea mode wire argocd service is being binded to helmServiceImpl due to which we are restricted to implement this here.
// RefreshAndUpdateACDApp this will update chart info in acd app if required in case of mono repo migration and will refresh argo app
func (impl *EAModeDeploymentServiceImpl) UpdateAndSyncACDApps(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, chartGitAttribute *commonBean.ChartGitAttribute, isMonoRepoMigrationRequired bool, ctx context.Context, tx *pg.Tx) error {
	return errors.New("this is not implemented")
}
func (impl *EAModeDeploymentServiceImpl) ValidateCustomGitOpsConfig(request validationBean.ValidateGitOpsRepoRequest) (string, bool, error) {
	return "", false, errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) GetGitRepoUrl(gitOpsRepoName string) (string, error) {
	return "", errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) UpdateValuesDependencies(installAppVersionRequest *appStoreBean.InstallAppVersionDTO) error {
	return errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) GitOpsOperations(manifestResponse *bean.AppStoreManifestResponse, installAppVersionRequest *appStoreBean.InstallAppVersionDTO) (*bean.AppStoreGitOpsResponse, error) {
	return nil, errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) GenerateManifestAndPerformGitOperations(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, appStoreApplicationVersion *appStoreDiscoverRepository.AppStoreApplicationVersion) (*bean.AppStoreGitOpsResponse, error) {
	return nil, errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) GenerateManifest(installAppVersionRequest *appStoreBean.InstallAppVersionDTO, appStoreApplicationVersion *appStoreDiscoverRepository.AppStoreApplicationVersion) (manifestResponse *bean.AppStoreManifestResponse, err error) {
	return nil, errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) CheckIfArgoAppExists(acdAppName string) (isFound bool, err error) {
	return isFound, errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) UpdateAppGitOpsOperations(manifest *bean.AppStoreManifestResponse, installAppVersionRequest *appStoreBean.InstallAppVersionDTO, monoRepoMigrationRequired bool, commitRequirements bool) (*bean.AppStoreGitOpsResponse, error) {
	return nil, errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) GetChartBytesForLatestDeployment(installedAppId int, installedAppVersionId int) ([]byte, error) {
	return nil, errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) GetChartBytesForParticularDeployment(installedAppId int, installedAppVersionId int, installedAppVersionHistoryId int) ([]byte, error) {
	return nil, errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) DeleteACD(acdAppName string, ctx context.Context, isNonCascade bool) error {
	return errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) GetAcdAppGitOpsRepoURL(appName string, environmentName string) (string, error) {
	return "", errors.New("this is not implemented")
}

func (impl *EAModeDeploymentServiceImpl) CreateArgoRepoSecretIfNeeded(appStoreApplicationVersion *appStoreDiscoverRepository.AppStoreApplicationVersion) error {
	return errors.New("this is not implemented")
}

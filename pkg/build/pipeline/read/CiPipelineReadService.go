/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package read

import (
	"errors"
	"github.com/devtron-labs/devtron/internal/sql/repository"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/build/pipeline/read/adapter"
	"github.com/devtron-labs/devtron/pkg/build/pipeline/read/bean"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type CiPipelineConfigReadService interface {
	FindLinkedCiCount(ciPipelineId int) (int, error)
	FindNumberOfAppsWithCiPipeline(appIds []int) (count int, err error)
	FindAllPipelineCreatedCountInLast24Hour() (pipelineCount int, err error)
	FindAllDeletedPipelineCountInLast24Hour() (pipelineCount int, err error)
	GetChildrenCiCount(parentCiPipelineId int) (int, error)
	GetCiPipelineById(ciPipelineId int) (*bean.CiPipelineMin, error)
	GetDockerRegistryIdForCiPipeline(ciPipelineId int, artifact *repository.CiArtifact) (*string, error)
}

type CiPipelineConfigReadServiceImpl struct {
	logger                       *zap.SugaredLogger
	ciPipelineRepository         pipelineConfig.CiPipelineRepository
	ciTemplateOverrideRepository pipelineConfig.CiTemplateOverrideRepository
}

func NewCiPipelineConfigReadServiceImpl(
	logger *zap.SugaredLogger,
	ciPipelineRepository pipelineConfig.CiPipelineRepository,
	ciTemplateOverrideRepository pipelineConfig.CiTemplateOverrideRepository,
) *CiPipelineConfigReadServiceImpl {
	return &CiPipelineConfigReadServiceImpl{
		logger:                       logger,
		ciPipelineRepository:         ciPipelineRepository,
		ciTemplateOverrideRepository: ciTemplateOverrideRepository,
	}
}

func (impl *CiPipelineConfigReadServiceImpl) FindLinkedCiCount(ciPipelineId int) (int, error) {
	return impl.ciPipelineRepository.FindLinkedCiCount(ciPipelineId)
}

func (impl *CiPipelineConfigReadServiceImpl) FindNumberOfAppsWithCiPipeline(appIds []int) (count int, err error) {
	return impl.ciPipelineRepository.FindNumberOfAppsWithCiPipeline(appIds)
}

func (impl *CiPipelineConfigReadServiceImpl) FindAllPipelineCreatedCountInLast24Hour() (pipelineCount int, err error) {
	return impl.ciPipelineRepository.FindAllPipelineCreatedCountInLast24Hour()
}

func (impl *CiPipelineConfigReadServiceImpl) FindAllDeletedPipelineCountInLast24Hour() (pipelineCount int, err error) {
	return impl.ciPipelineRepository.FindAllDeletedPipelineCountInLast24Hour()
}

func (impl *CiPipelineConfigReadServiceImpl) GetChildrenCiCount(parentCiPipelineId int) (int, error) {
	count, err := impl.ciPipelineRepository.GetChildrenCiCount(parentCiPipelineId)
	if err != nil && !errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("failed to get children ci count", "parentCiPipelineId", parentCiPipelineId, "error", err)
		return 0, err
	} else if errors.Is(err, pg.ErrNoRows) {
		impl.logger.Debugw("no children ci found", "parentCiPipelineId", parentCiPipelineId)
		return 0, nil
	}
	return count, nil
}

func (impl *CiPipelineConfigReadServiceImpl) GetCiPipelineById(ciPipelineId int) (*bean.CiPipelineMin, error) {
	ciPipeline, err := impl.ciPipelineRepository.FindOneWithMinData(ciPipelineId)
	if err != nil {
		impl.logger.Errorw("error in getting ciPipeline by id", "ciPipelineId", ciPipelineId, "err", err)
		return nil, err
	}
	return adapter.NewCiPipelineMin(ciPipeline)
}

func (impl *CiPipelineConfigReadServiceImpl) GetDockerRegistryIdForCiPipeline(ciPipelineId int, artifact *repository.CiArtifact) (*string, error) {
	ciPipeline, err := impl.ciPipelineRepository.FindById(ciPipelineId)
	if err != nil {
		impl.logger.Errorw("error in fetching ciPipeline", "ciPipelineId", ciPipelineId, "error", err)
		return nil, err
	}

	if ciPipeline.IsExternal && ciPipeline.ParentCiPipeline == 0 {
		impl.logger.Warn("Ignoring for external ci")
		return nil, nil
	}

	if ciPipeline.CiTemplate == nil {
		impl.logger.Warn("returning as ciPipeline.CiTemplate is found nil")
		return nil, nil
	}
	var dockerRegistryId string
	if artifact.DataSource == repository.POST_CI || artifact.DataSource == repository.PRE_CD || artifact.DataSource == repository.POST_CD {
		// if image is generated by plugin at these stages
		if artifact.CredentialsSourceType == repository.GLOBAL_CONTAINER_REGISTRY {
			dockerRegistryId = artifact.CredentialSourceValue
		}
	} else if artifact.DataSource == repository.CI_RUNNER {
		// if image is created by ci build
		dockerRegistryId = *ciPipeline.CiTemplate.DockerRegistryId
		if len(dockerRegistryId) == 0 {
			impl.logger.Warn("returning as dockerRegistryId is found empty")
			return nil, nil
		}

		if ciPipeline.IsDockerConfigOverridden {
			//set dockerRegistryId value with the DockerRegistryId of the overridden dockerRegistry
			ciPipId := ciPipelineId
			if ciPipeline.ParentCiPipeline != 0 {
				ciPipId = ciPipeline.ParentCiPipeline
			}
			ciTemplateOverride, err := impl.ciTemplateOverrideRepository.FindByCiPipelineId(ciPipId)
			if err != nil {
				impl.logger.Errorw("error in getting ciTemplateOverride by ciPipelineId", "ciPipelineId", ciPipelineId, "error", err)
				return nil, err
			}
			dockerRegistryId = ciTemplateOverride.DockerRegistryId
		}
	}
	return &dockerRegistryId, nil
}

// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	"github.com/devtron-labs/devtron/pkg/build/pipeline/bean"
	mock "github.com/stretchr/testify/mock"
)

// CiBuildConfigService is an autogenerated mock type for the CiBuildConfigService type
type CiBuildConfigService struct {
	mock.Mock
}

// Delete provides a mock function with given fields: ciBuildConfigId
func (_m *CiBuildConfigService) Delete(ciBuildConfigId int) error {
	ret := _m.Called(ciBuildConfigId)

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(ciBuildConfigId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetCountByBuildType provides a mock function with given fields:
func (_m *CiBuildConfigService) GetCountByBuildType() map[bean.CiBuildType]int {
	ret := _m.Called()

	var r0 map[bean.CiBuildType]int
	if rf, ok := ret.Get(0).(func() map[bean.CiBuildType]int); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[bean.CiBuildType]int)
		}
	}

	return r0
}

// Save provides a mock function with given fields: templateId, overrideTemplateId, ciBuildConfigBean, userId
func (_m *CiBuildConfigService) Save(templateId int, overrideTemplateId int, ciBuildConfigBean *bean.CiBuildConfigBean, userId int32) error {
	ret := _m.Called(templateId, overrideTemplateId, ciBuildConfigBean, userId)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int, *bean.CiBuildConfigBean, int32) error); ok {
		r0 = rf(templateId, overrideTemplateId, ciBuildConfigBean, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateOrSave provides a mock function with given fields: templateId, overrideTemplateId, ciBuildConfig, userId
func (_m *CiBuildConfigService) UpdateOrSave(templateId int, overrideTemplateId int, ciBuildConfig *bean.CiBuildConfigBean, userId int32) (*bean.CiBuildConfigBean, error) {
	ret := _m.Called(templateId, overrideTemplateId, ciBuildConfig, userId)

	var r0 *bean.CiBuildConfigBean
	if rf, ok := ret.Get(0).(func(int, int, *bean.CiBuildConfigBean, int32) *bean.CiBuildConfigBean); ok {
		r0 = rf(templateId, overrideTemplateId, ciBuildConfig, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CiBuildConfigBean)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int, *bean.CiBuildConfigBean, int32) error); ok {
		r1 = rf(templateId, overrideTemplateId, ciBuildConfig, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewCiBuildConfigService interface {
	mock.TestingT
	Cleanup(func())
}

// NewCiBuildConfigService creates a new instance of CiBuildConfigService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCiBuildConfigService(t mockConstructorTestingTNewCiBuildConfigService) *CiBuildConfigService {
	mock := &CiBuildConfigService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

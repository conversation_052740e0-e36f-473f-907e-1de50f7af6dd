// Code generated by mockery v2.18.0. DO NOT EDIT.

package mocks

import (
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/pkg/genericNotes/repository"
)

// GenericNoteRepository is an autogenerated mock type for the GenericNoteRepository type
type GenericNoteRepository struct {
	mock.Mock
}

// CommitTx provides a mock function with given fields: tx
func (_m *GenericNoteRepository) CommitTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindByAppId provides a mock function with given fields: id
func (_m *GenericNoteRepository) FindByAppId(id int) (*repository.GenericNote, error) {
	ret := _m.Called(id)

	var r0 *repository.GenericNote
	if rf, ok := ret.Get(0).(func(int) *repository.GenericNote); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.GenericNote)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByClusterId provides a mock function with given fields: id
func (_m *GenericNoteRepository) FindByClusterId(id int) (*repository.GenericNote, error) {
	ret := _m.Called(id)

	var r0 *repository.GenericNote
	if rf, ok := ret.Get(0).(func(int) *repository.GenericNote); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.GenericNote)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdentifier provides a mock function with given fields: identifier, identifierType
func (_m *GenericNoteRepository) FindByIdentifier(identifier int, identifierType repository.NoteType) (*repository.GenericNote, error) {
	ret := _m.Called(identifier, identifierType)

	var r0 *repository.GenericNote
	if rf, ok := ret.Get(0).(func(int, repository.NoteType) *repository.GenericNote); ok {
		r0 = rf(identifier, identifierType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.GenericNote)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, repository.NoteType) error); ok {
		r1 = rf(identifier, identifierType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDescriptionFromAppIds provides a mock function with given fields: appIds
func (_m *GenericNoteRepository) GetDescriptionFromAppIds(appIds []int) ([]*repository.GenericNote, error) {
	ret := _m.Called(appIds)

	var r0 []*repository.GenericNote
	if rf, ok := ret.Get(0).(func([]int) []*repository.GenericNote); ok {
		r0 = rf(appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.GenericNote)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetGenericNotesForAppIds provides a mock function with given fields: appIds
func (_m *GenericNoteRepository) GetGenericNotesForAppIds(appIds []int) ([]*repository.GenericNote, error) {
	ret := _m.Called(appIds)

	var r0 []*repository.GenericNote
	if rf, ok := ret.Get(0).(func([]int) []*repository.GenericNote); ok {
		r0 = rf(appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.GenericNote)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RollbackTx provides a mock function with given fields: tx
func (_m *GenericNoteRepository) RollbackTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Save provides a mock function with given fields: tx, model
func (_m *GenericNoteRepository) Save(tx *pg.Tx, model *repository.GenericNote) error {
	ret := _m.Called(tx, model)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, *repository.GenericNote) error); ok {
		r0 = rf(tx, model)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StartTx provides a mock function with given fields:
func (_m *GenericNoteRepository) StartTx() (*pg.Tx, error) {
	ret := _m.Called()

	var r0 *pg.Tx
	if rf, ok := ret.Get(0).(func() *pg.Tx); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.Tx)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: tx, model
func (_m *GenericNoteRepository) Update(tx *pg.Tx, model *repository.GenericNote) error {
	ret := _m.Called(tx, model)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, *repository.GenericNote) error); ok {
		r0 = rf(tx, model)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewGenericNoteRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewGenericNoteRepository creates a new instance of GenericNoteRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewGenericNoteRepository(t mockConstructorTestingTNewGenericNoteRepository) *GenericNoteRepository {
	mock := &GenericNoteRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

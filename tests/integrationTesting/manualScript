pre-requisite - docker

- run "docker run -e TEST_BRANCH='TEST_BRANCH' -e LATEST_HASH='LATEST_HASH' --privileged -d --name dind-test docker:dind"
- run "docker exec -it dind-test /bin/sh"
- run "apk add wget curl vim bash git"
- run "wget -q -O - https://raw.githubusercontent.com/k3d-io/k3d/main/install.sh | bash"
- run "k3d cluster create it-cluster"
- run 'curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"'
- run "install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl"
- run "vim postgresql-secret.yaml" [input data]
- run "kubectl apply -f postgresql-secret.yaml"
- run "vim postgresql.yaml" [input data]
- run "kubectl apply -f postgresql.yaml"
- run "vim migrator.yaml" [input data]
- run "kubectl apply -f migrator.yaml"
- run "git clone https://github.com/devtron-labs/devtron.git"
- run "cd devtron"
- run "git checkout $TEST_BRANCH"
- run "go test ./tests/integrationTesting"
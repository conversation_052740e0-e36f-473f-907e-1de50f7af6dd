{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Description of all APIs", "title": "Consolidate Services", "version": "version not set"}, "paths": {"/api/v1/account/password": {"put": {"tags": ["AccountService"], "summary": "UpdatePassword updates an account's password to a new value", "operationId": "UpdatePassword", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/accountUpdatePasswordRequest"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/accountUpdatePasswordResponse"}}}}}, "/api/v1/applications": {"get": {"tags": ["ApplicationService"], "summary": "List returns list of applications", "operationId": "ListMixin6", "parameters": [{"type": "string", "name": "name", "in": "query"}, {"type": "string", "name": "refresh", "in": "query"}, {"type": "array", "items": {"type": "string"}, "name": "project", "in": "query"}, {"type": "string", "name": "resourceVersion", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1ApplicationList"}}}}, "post": {"tags": ["ApplicationService"], "summary": "Create creates an application", "operationId": "CreateMixin6", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1Application"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Application"}}}}}, "/api/v1/applications/{application.metadata.name}": {"put": {"tags": ["ApplicationService"], "summary": "Update updates an application", "operationId": "UpdateMixin6", "parameters": [{"type": "string", "name": "application.metadata.name", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1Application"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Application"}}}}}, "/api/v1/applications/{applicationName}/managed-resources": {"get": {"tags": ["ApplicationService"], "operationId": "ManagedResources", "parameters": [{"type": "string", "name": "applicationName", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/applicationManagedResourcesResponse"}}}}}, "/api/v1/applications/{applicationName}/resource-tree": {"get": {"tags": ["ApplicationService"], "operationId": "ResourceTree", "parameters": [{"type": "string", "name": "applicationName", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1ApplicationTree"}}}}}, "/api/v1/applications/{name}": {"get": {"tags": ["ApplicationService"], "summary": "Get returns an application by name", "operationId": "GetMixin6", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"type": "string", "name": "refresh", "in": "query"}, {"type": "array", "items": {"type": "string"}, "name": "project", "in": "query"}, {"type": "string", "name": "resourceVersion", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Application"}}}}, "delete": {"tags": ["ApplicationService"], "summary": "Delete deletes an application", "operationId": "DeleteMixin6", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/applicationApplicationResponse"}}}}, "patch": {"tags": ["ApplicationService"], "summary": "Patch patch an application", "operationId": "Patch", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/applicationApplicationPatchRequest"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Application"}}}}}, "/api/v1/applications/{name}/events": {"get": {"tags": ["ApplicationService"], "summary": "ListResourceEvents returns a list of event resources", "operationId": "ListResourceEvents", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"type": "string", "name": "resourceNamespace", "in": "query"}, {"type": "string", "name": "resourceName", "in": "query"}, {"type": "string", "name": "resourceUID", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1EventList"}}}}}, "/api/v1/applications/{name}/manifests": {"get": {"tags": ["ApplicationService"], "summary": "GetManifests returns application manifests", "operationId": "GetManifests", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"type": "string", "name": "revision", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/repositoryManifestResponse"}}}}}, "/api/v1/applications/{name}/operation": {"delete": {"tags": ["ApplicationService"], "summary": "TerminateOperation terminates the currently running operation", "operationId": "TerminateOperation", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/applicationOperationTerminateResponse"}}}}}, "/api/v1/applications/{name}/pods/{podName}/logs": {"get": {"tags": ["ApplicationService"], "summary": "PodLogs returns stream of log entries for the specified pod. Pod", "operationId": "PodLogs", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"type": "string", "name": "podName", "in": "path", "required": true}, {"type": "string", "name": "namespace", "in": "query"}, {"type": "string", "name": "container", "in": "query"}, {"type": "string", "format": "int64", "name": "sinceSeconds", "in": "query"}, {"type": "string", "format": "int64", "description": "Represents seconds of UTC time since Unix epoch\n1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to\n9999-12-31T23:59:59Z inclusive.", "name": "sinceTime.seconds", "in": "query"}, {"type": "integer", "format": "int32", "description": "Non-negative fractions of a second at nanosecond resolution. Negative\nsecond values with fractions must still have non-negative nanos values\nthat count forward in time. Must be from 0 to 999,999,999\ninclusive. This field may be limited in precision depending on context.", "name": "sinceTime.nanos", "in": "query"}, {"type": "string", "format": "int64", "name": "tailLines", "in": "query"}, {"type": "boolean", "format": "boolean", "name": "follow", "in": "query"}], "responses": {"200": {"description": "(streaming responses)", "schema": {"$ref": "#/definitions/applicationLogEntry"}}}}}, "/api/v1/applications/{name}/resource": {"get": {"tags": ["ApplicationService"], "summary": "GetResource returns single application resource", "operationId": "GetResource", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"type": "string", "name": "namespace", "in": "query"}, {"type": "string", "name": "resourceName", "in": "query"}, {"type": "string", "name": "version", "in": "query"}, {"type": "string", "name": "group", "in": "query"}, {"type": "string", "name": "kind", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/applicationApplicationResourceResponse"}}}}, "post": {"tags": ["ApplicationService"], "summary": "PatchResource patch single application resource", "operationId": "PatchResource", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/applicationApplicationResourceResponse"}}}}, "delete": {"tags": ["ApplicationService"], "summary": "DeleteResource deletes a single application resource", "operationId": "DeleteResource", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/applicationApplicationResponse"}}}}}, "/api/v1/applications/{name}/resource/actions": {"get": {"tags": ["ApplicationService"], "operationId": "ListResourceActions", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"type": "string", "name": "namespace", "in": "query"}, {"type": "string", "name": "resourceName", "in": "query"}, {"type": "string", "name": "version", "in": "query"}, {"type": "string", "name": "group", "in": "query"}, {"type": "string", "name": "kind", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/applicationResourceActionsListResponse"}}}}, "post": {"tags": ["ApplicationService"], "operationId": "RunResourceAction", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/applicationApplicationResponse"}}}}}, "/api/v1/applications/{name}/revisions/{revision}/metadata": {"get": {"tags": ["ApplicationService"], "summary": "Get the meta-data (author, date, tags, message) for a specific revision of the application", "operationId": "RevisionMetadata", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"type": "string", "name": "revision", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1RevisionMetadata"}}}}}, "/api/v1/applications/{name}/rollback": {"post": {"tags": ["ApplicationService"], "summary": "Rollback syncs an application to its target state", "operationId": "Rollback", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/applicationApplicationRollbackRequest"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Application"}}}}}, "/api/v1/applications/{name}/spec": {"put": {"tags": ["ApplicationService"], "summary": "UpdateSpec updates an application spec", "operationId": "UpdateSpec", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1ApplicationSpec"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1ApplicationSpec"}}}}}, "/api/v1/applications/{name}/sync": {"post": {"tags": ["ApplicationService"], "summary": "Sync syncs an application to its target state", "operationId": "Sync", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/applicationApplicationSyncRequest"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Application"}}}}}, "/api/v1/certificates": {"get": {"tags": ["CertificateService"], "summary": "List all available repository certificates", "operationId": "ListCertificates", "parameters": [{"type": "string", "description": "A file-glob pattern (not regular expression) the host name has to match.", "name": "hostNamePattern", "in": "query"}, {"type": "string", "description": "The type of the certificate to match (ssh or https).", "name": "certType", "in": "query"}, {"type": "string", "description": "The sub type of the certificate to match (protocol dependent, usually only used for ssh certs).", "name": "certSubType", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1RepositoryCertificateList"}}}}, "post": {"tags": ["CertificateService"], "summary": "Creates repository certificates on the server", "operationId": "CreateCertificate", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1RepositoryCertificateList"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1RepositoryCertificateList"}}}}, "delete": {"tags": ["CertificateService"], "summary": "Delete the certificates that match the RepositoryCertificateQuery", "operationId": "DeleteCertificate", "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1RepositoryCertificateList"}}}}}, "/api/v1/clusters": {"get": {"tags": ["ClusterService"], "summary": "List returns list of clusters", "operationId": "List", "parameters": [{"type": "string", "name": "server", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1ClusterList"}}}}, "post": {"tags": ["ClusterService"], "summary": "Create creates a cluster", "operationId": "Create", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1Cluster"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Cluster"}}}}}, "/api/v1/clusters/{cluster.server}": {"put": {"tags": ["ClusterService"], "summary": "Update updates a cluster", "operationId": "Update", "parameters": [{"type": "string", "name": "cluster.server", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1Cluster"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Cluster"}}}}}, "/api/v1/clusters/{server}": {"get": {"tags": ["ClusterService"], "summary": "Get returns a cluster by server address", "operationId": "GetMixin1", "parameters": [{"type": "string", "name": "server", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Cluster"}}}}, "delete": {"tags": ["ClusterService"], "summary": "Delete deletes a cluster", "operationId": "Delete", "parameters": [{"type": "string", "name": "server", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/clusterClusterResponse"}}}}}, "/api/v1/clusters/{server}/rotate-auth": {"post": {"tags": ["ClusterService"], "summary": "RotateAuth returns a cluster by server address", "operationId": "RotateAuth", "parameters": [{"type": "string", "name": "server", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/clusterClusterResponse"}}}}}, "/api/v1/projects": {"get": {"tags": ["ProjectService"], "summary": "List returns list of projects", "operationId": "ListMixin4", "parameters": [{"type": "string", "name": "name", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1AppProjectList"}}}}, "post": {"tags": ["ProjectService"], "summary": "Create a new project.", "operationId": "CreateMixin4", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/projectProjectCreateRequest"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1AppProject"}}}}}, "/api/v1/projects/{name}": {"get": {"tags": ["ProjectService"], "summary": "Get returns a project by name", "operationId": "GetMixin4", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1AppProject"}}}}, "delete": {"tags": ["ProjectService"], "summary": "Delete deletes a project", "operationId": "DeleteMixin4", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/projectEmptyResponse"}}}}}, "/api/v1/projects/{name}/events": {"get": {"tags": ["ProjectService"], "summary": "ListEvents returns a list of project events", "operationId": "ListEvents", "parameters": [{"type": "string", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1EventList"}}}}}, "/api/v1/projects/{project.metadata.name}": {"put": {"tags": ["ProjectService"], "summary": "Update updates a project", "operationId": "UpdateMixin4", "parameters": [{"type": "string", "name": "project.metadata.name", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/projectProjectUpdateRequest"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1AppProject"}}}}}, "/api/v1/projects/{project}/roles/{role}/token": {"post": {"tags": ["ProjectService"], "summary": "Create a new project token.", "operationId": "CreateToken", "parameters": [{"type": "string", "name": "project", "in": "path", "required": true}, {"type": "string", "name": "role", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/projectProjectTokenCreateRequest"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/projectProjectTokenResponse"}}}}}, "/api/v1/projects/{project}/roles/{role}/token/{iat}": {"delete": {"tags": ["ProjectService"], "summary": "Delete a new project token.", "operationId": "DeleteToken", "parameters": [{"type": "string", "name": "project", "in": "path", "required": true}, {"type": "string", "name": "role", "in": "path", "required": true}, {"type": "string", "format": "int64", "name": "iat", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/projectEmptyResponse"}}}}}, "/api/v1/repositories": {"get": {"tags": ["RepositoryService"], "summary": "List returns list of repos", "operationId": "ListMixin2", "parameters": [{"type": "string", "name": "repo", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1RepositoryList"}}}}, "post": {"tags": ["RepositoryService"], "summary": "Create creates a repo", "operationId": "CreateMixin2", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1Repository"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Repository"}}}}}, "/api/v1/repositories/{repo.repo}": {"put": {"tags": ["RepositoryService"], "summary": "Update updates a repo", "operationId": "UpdateMixin2", "parameters": [{"type": "string", "name": "repo.repo", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1Repository"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/v1alpha1Repository"}}}}}, "/api/v1/repositories/{repo}": {"delete": {"tags": ["RepositoryService"], "summary": "Delete deletes a repo", "operationId": "DeleteMixin2", "parameters": [{"type": "string", "name": "repo", "in": "path", "required": true}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/repositoryRepoResponse"}}}}}, "/api/v1/repositories/{repo}/apps": {"get": {"tags": ["RepositoryService"], "summary": "ListApps returns list of apps in the repo", "operationId": "ListApps", "parameters": [{"type": "string", "name": "repo", "in": "path", "required": true}, {"type": "string", "name": "revision", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/repositoryRepoAppsResponse"}}}}}, "/api/v1/repositories/{repo}/apps/{path}": {"get": {"tags": ["RepositoryService"], "summary": "GetAppDetails returns application details by given path", "operationId": "GetAppDetails", "parameters": [{"type": "string", "name": "repo", "in": "path", "required": true}, {"type": "string", "name": "path", "in": "path", "required": true}, {"type": "string", "name": "revision", "in": "query"}, {"type": "array", "items": {"type": "string"}, "name": "helm.valueFiles", "in": "query"}, {"type": "string", "name": "ksonnet.environment", "in": "query"}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/repositoryRepoAppDetailsResponse"}}}}}, "/api/v1/repositories/{repo}/validate": {"post": {"tags": ["RepositoryService"], "summary": "ValidateAccess validates access to a repository with given parameters", "operationId": "ValidateAccess", "parameters": [{"type": "string", "name": "repo", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/repositoryRepoResponse"}}}}}, "/api/v1/session": {"post": {"tags": ["SessionService"], "summary": "Create a new JWT for authentication and set a cookie if using HTTP.", "operationId": "CreateMixin8", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sessionSessionCreateRequest"}}], "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/sessionSessionResponse"}}}}, "delete": {"tags": ["SessionService"], "summary": "Delete an existing JWT cookie if using HTTP.", "operationId": "DeleteMixin8", "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/sessionSessionResponse"}}}}}, "/api/v1/settings": {"get": {"tags": ["SettingsService"], "summary": "Get returns Argo CD settings", "operationId": "Get", "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/clusterSettings"}}}}}, "/api/v1/stream/applications": {"get": {"tags": ["ApplicationService"], "summary": "Watch returns stream of application change events.", "operationId": "Watch", "parameters": [{"type": "string", "name": "name", "in": "query"}, {"type": "string", "name": "refresh", "in": "query"}, {"type": "array", "items": {"type": "string"}, "name": "project", "in": "query"}, {"type": "string", "name": "resourceVersion", "in": "query"}], "responses": {"200": {"description": "(streaming responses)", "schema": {"$ref": "#/definitions/v1alpha1ApplicationWatchEvent"}}}}}, "/api/version": {"get": {"tags": ["VersionService"], "summary": "Version returns version information of the API server", "operationId": "Version", "responses": {"200": {"description": "(empty)", "schema": {"$ref": "#/definitions/versionVersionMessage"}}}}}}, "definitions": {"accountUpdatePasswordRequest": {"type": "object", "properties": {"currentPassword": {"type": "string"}, "newPassword": {"type": "string"}}}, "accountUpdatePasswordResponse": {"type": "object"}, "applicationApplicationPatchRequest": {"type": "object", "title": "ApplicationPatchRequest is a request to patch an application", "properties": {"name": {"type": "string"}, "patch": {"type": "string"}, "patchType": {"type": "string"}}}, "applicationApplicationResourceResponse": {"type": "object", "properties": {"manifest": {"type": "string"}}}, "applicationApplicationResponse": {"type": "object"}, "applicationApplicationRollbackRequest": {"type": "object", "properties": {"dryRun": {"type": "boolean", "format": "boolean"}, "id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "prune": {"type": "boolean", "format": "boolean"}}}, "applicationApplicationSyncRequest": {"type": "object", "title": "ApplicationSyncRequest is a request to apply the config state to live state", "properties": {"dryRun": {"type": "boolean", "format": "boolean"}, "manifests": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "prune": {"type": "boolean", "format": "boolean"}, "resources": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1SyncOperationResource"}}, "revision": {"type": "string"}, "strategy": {"$ref": "#/definitions/v1alpha1SyncStrategy"}}}, "applicationLogEntry": {"type": "object", "properties": {"content": {"type": "string"}, "timeStamp": {"$ref": "#/definitions/v1Time"}}}, "applicationManagedResourcesResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1ResourceDiff"}}}}, "applicationOperationTerminateResponse": {"type": "object"}, "applicationResourceActionsListResponse": {"type": "object", "properties": {"actions": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1ResourceAction"}}}}, "clusterClusterResponse": {"type": "object"}, "clusterConnector": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}}}, "clusterDexConfig": {"type": "object", "properties": {"connectors": {"type": "array", "items": {"$ref": "#/definitions/clusterConnector"}}}}, "clusterGoogleAnalyticsConfig": {"type": "object", "properties": {"anonymizeUsers": {"type": "boolean", "format": "boolean"}, "trackingID": {"type": "string"}}}, "clusterHelp": {"type": "object", "title": "Help settings", "properties": {"chatText": {"type": "string", "title": "the text for getting chat help, defaults to \"Chat now!\""}, "chatUrl": {"type": "string", "title": "the URL for getting chat help, this will typically be your Slack channel for support"}}}, "clusterOIDCConfig": {"type": "object", "properties": {"cliClientID": {"type": "string"}, "clientID": {"type": "string"}, "issuer": {"type": "string"}, "name": {"type": "string"}, "scopes": {"type": "array", "items": {"type": "string"}}}}, "clusterSettings": {"type": "object", "properties": {"appLabelKey": {"type": "string"}, "dexConfig": {"$ref": "#/definitions/clusterDexConfig"}, "googleAnalytics": {"$ref": "#/definitions/clusterGoogleAnalyticsConfig"}, "help": {"$ref": "#/definitions/clusterHelp"}, "kustomizeOptions": {"$ref": "#/definitions/v1alpha1KustomizeOptions"}, "oidcConfig": {"$ref": "#/definitions/clusterOIDCConfig"}, "resourceOverrides": {"type": "object", "additionalProperties": {"$ref": "#/definitions/v1alpha1ResourceOverride"}}, "statusBadgeEnabled": {"type": "boolean", "format": "boolean"}, "url": {"type": "string"}}}, "projectEmptyResponse": {"type": "object"}, "projectProjectCreateRequest": {"description": "ProjectCreateRequest defines project creation parameters.", "type": "object", "properties": {"project": {"$ref": "#/definitions/v1alpha1AppProject"}}}, "projectProjectTokenCreateRequest": {"description": "ProjectTokenCreateRequest defines project token creation parameters.", "type": "object", "properties": {"description": {"type": "string"}, "expiresIn": {"type": "string", "format": "int64", "title": "expiresIn represents a duration in seconds"}, "project": {"type": "string"}, "role": {"type": "string"}}}, "projectProjectTokenResponse": {"description": "ProjectTokenResponse wraps the created token or returns an empty string if deleted.", "type": "object", "properties": {"token": {"type": "string"}}}, "projectProjectUpdateRequest": {"type": "object", "properties": {"project": {"$ref": "#/definitions/v1alpha1AppProject"}}}, "repositoryAppInfo": {"type": "object", "title": "AppInfo contains application type and app file path", "properties": {"path": {"type": "string"}, "type": {"type": "string"}}}, "repositoryDirectoryAppSpec": {"type": "object", "title": "DirectoryAppSpec contains directory"}, "repositoryHelmAppDetailsQuery": {"type": "object", "properties": {"valueFiles": {"type": "array", "items": {"type": "string"}}}}, "repositoryHelmAppSpec": {"type": "object", "title": "HelmAppSpec contains helm app name and path in source repo", "properties": {"name": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1HelmParameter"}}, "path": {"type": "string"}, "valueFiles": {"type": "array", "items": {"type": "string"}}}}, "repositoryKsonnetAppDetailsQuery": {"type": "object", "properties": {"environment": {"type": "string"}}}, "repositoryKsonnetAppSpec": {"type": "object", "title": "KsonnetAppSpec contains Ksonnet app response\nThis roughly reflects: ksonnet/ksonnet/metadata/app/schema.go", "properties": {"environments": {"type": "object", "additionalProperties": {"$ref": "#/definitions/repositoryKsonnetEnvironment"}}, "name": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1KsonnetParameter"}}, "path": {"type": "string"}}}, "repositoryKsonnetEnvironment": {"type": "object", "properties": {"destination": {"$ref": "#/definitions/repositoryKsonnetEnvironmentDestination"}, "k8sVersion": {"description": "KubernetesVersion is the kubernetes version the targetted cluster is running on.", "type": "string"}, "name": {"type": "string", "title": "Name is the user defined name of an environment"}, "path": {"description": "Path is the relative project path containing metadata for this environment.", "type": "string"}}}, "repositoryKsonnetEnvironmentDestination": {"type": "object", "properties": {"namespace": {"type": "string", "title": "Namespace is the namespace of the Kubernetes server that targets should be deployed to"}, "server": {"description": "Server is the Kubernetes server that the cluster is running on.", "type": "string"}}}, "repositoryKustomizeAppSpec": {"type": "object", "title": "KustomizeAppSpec contains kustomize app name and path in source repo", "properties": {"images": {"description": "images is a list of available images.", "type": "array", "items": {"type": "string"}}, "path": {"type": "string"}}}, "repositoryManifestResponse": {"type": "object", "properties": {"manifests": {"type": "array", "items": {"type": "string"}}, "namespace": {"type": "string"}, "revision": {"type": "string"}, "server": {"type": "string"}, "sourceType": {"type": "string"}}}, "repositoryRepoAppDetailsResponse": {"type": "object", "title": "RepoAppDetailsResponse application details", "properties": {"directory": {"$ref": "#/definitions/repositoryDirectoryAppSpec"}, "helm": {"$ref": "#/definitions/repositoryHelmAppSpec"}, "ksonnet": {"$ref": "#/definitions/repositoryKsonnetAppSpec"}, "kustomize": {"$ref": "#/definitions/repositoryKustomizeAppSpec"}, "type": {"type": "string"}}}, "repositoryRepoAppsResponse": {"type": "object", "title": "RepoAppsResponse contains applications of specified repository", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/repositoryAppInfo"}}}}, "repositoryRepoResponse": {"type": "object"}, "sessionSessionCreateRequest": {"description": "SessionCreateRequest is for logging in.", "type": "object", "properties": {"password": {"type": "string"}, "token": {"type": "string"}, "username": {"type": "string"}}}, "sessionSessionResponse": {"description": "SessionResponse wraps the created token or returns an empty string if deleted.", "type": "object", "properties": {"token": {"type": "string"}}}, "v1Event": {"description": "Event is a report of an event somewhere in the cluster.", "type": "object", "properties": {"action": {"type": "string", "title": "What action was taken/failed regarding to the Regarding object.\n+optional"}, "count": {"type": "integer", "format": "int32", "title": "The number of times this event has occurred.\n+optional"}, "eventTime": {"$ref": "#/definitions/v1MicroTime"}, "firstTimestamp": {"$ref": "#/definitions/v1Time"}, "involvedObject": {"$ref": "#/definitions/v1ObjectReference"}, "lastTimestamp": {"$ref": "#/definitions/v1Time"}, "message": {"type": "string", "title": "A human-readable description of the status of this operation.\nTODO: decide on maximum length.\n+optional"}, "metadata": {"$ref": "#/definitions/v1ObjectMeta"}, "reason": {"type": "string", "title": "This should be a short, machine understandable string that gives the reason\nfor the transition into the object's current status.\nTODO: provide exact specification for format.\n+optional"}, "related": {"$ref": "#/definitions/v1ObjectReference"}, "reportingComponent": {"type": "string", "title": "Name of the controller that emitted this Event, e.g. `kubernetes.io/kubelet`.\n+optional"}, "reportingInstance": {"type": "string", "title": "ID of the controller instance, e.g. `kubelet-xyzf`.\n+optional"}, "series": {"$ref": "#/definitions/v1EventSeries"}, "source": {"$ref": "#/definitions/v1EventSource"}, "type": {"type": "string", "title": "Type of this event (Normal, Warning), new types could be added in the future\n+optional"}}}, "v1EventList": {"description": "EventList is a list of events.", "type": "object", "properties": {"items": {"type": "array", "title": "List of events", "items": {"$ref": "#/definitions/v1Event"}}, "metadata": {"$ref": "#/definitions/v1ListMeta"}}}, "v1EventSeries": {"description": "EventSeries contain information on series of events, i.e. thing that was/is happening\ncontinuously for some time.", "type": "object", "properties": {"count": {"type": "integer", "format": "int32", "title": "Number of occurrences in this series up to the last heartbeat time"}, "lastObservedTime": {"$ref": "#/definitions/v1MicroTime"}, "state": {"type": "string", "title": "State of this Series: Ongoing or Finished"}}}, "v1EventSource": {"description": "EventSource contains information for an event.", "type": "object", "properties": {"component": {"type": "string", "title": "Component from which the event is generated.\n+optional"}, "host": {"type": "string", "title": "Node name on which the event is generated.\n+optional"}}}, "v1Fields": {"type": "object", "title": "Fields stores a set of fields in a data structure like a Trie.\nTo understand how this is used, see: https://github.com/kubernetes-sigs/structured-merge-diff", "properties": {"map": {"description": "Map stores a set of fields in a data structure like a Trie.\n\nEach key is either a '.' representing the field itself, and will always map to an empty set,\nor a string representing a sub-field or item. The string will follow one of these four formats:\n'f:<name>', where <name> is the name of a field in a struct, or key in a map\n'v:<value>', where <value> is the exact json formatted value of a list item\n'i:<index>', where <index> is position of a item in a list\n'k:<keys>', where <keys> is a map of  a list item's key fields to their unique values\nIf a key maps to an empty Fields value, the field that key represents is part of the set.\n\nThe exact format is defined in k8s.io/apiserver/pkg/endpoints/handlers/fieldmanager/internal", "type": "object", "additionalProperties": {"$ref": "#/definitions/v1Fields"}}}}, "v1GroupKind": {"description": "+protobuf.options.(gogoproto.goproto_stringer)=false", "type": "object", "title": "GroupKind specifies a Group and a Kind, but does not force a version.  This is useful for identifying\nconcepts during lookup stages without having partially valid types", "properties": {"group": {"type": "string"}, "kind": {"type": "string"}}}, "v1Initializer": {"description": "Initializer is information about an initializer that has not yet completed.", "type": "object", "properties": {"name": {"description": "name of the process that is responsible for initializing this object.", "type": "string"}}}, "v1Initializers": {"description": "Initializers tracks the progress of initialization.", "type": "object", "properties": {"pending": {"type": "array", "title": "Pending is a list of initializers that must execute in order before this object is visible.\nWhen the last pending initializer is removed, and no failing result is set, the initializers\nstruct will be set to nil and the object is considered as initialized and visible to all\nclients.\n+patchMergeKey=name\n+patchStrategy=merge", "items": {"$ref": "#/definitions/v1Initializer"}}, "result": {"$ref": "#/definitions/v1Status"}}}, "v1ListMeta": {"description": "ListMeta describes metadata that synthetic resources must have, including lists and\nvarious status objects. A resource may have only one of {ObjectMeta, ListMeta}.", "type": "object", "properties": {"continue": {"description": "continue may be set if the user set a limit on the number of items returned, and indicates that\nthe server has more data available. The value is opaque and may be used to issue another request\nto the endpoint that served this list to retrieve the next set of available objects. Continuing a\nconsistent list may not be possible if the server configuration has changed or more than a few\nminutes have passed. The resourceVersion field returned when using this continue value will be\nidentical to the value in the first response, unless you have received this token from an error\nmessage.", "type": "string"}, "resourceVersion": {"type": "string", "title": "String that identifies the server's internal version of this object that\ncan be used by clients to determine when objects have changed.\nValue must be treated as opaque by clients and passed unmodified back to the server.\nPopulated by the system.\nRead-only.\nMore info: https://git.k8s.io/community/contributors/devel/api-conventions.md#concurrency-control-and-consistency\n+optional"}, "selfLink": {"type": "string", "title": "selfLink is a URL representing this object.\nPopulated by the system.\nRead-only.\n+optional"}}}, "v1LoadBalancerIngress": {"description": "LoadBalancerIngress represents the status of a load-balancer ingress point:\ntraffic intended for the service should be sent to an ingress point.", "type": "object", "properties": {"hostname": {"type": "string", "title": "Hostname is set for load-balancer ingress points that are DNS based\n(typically AWS load-balancers)\n+optional"}, "ip": {"type": "string", "title": "IP is set for load-balancer ingress points that are IP based\n(typically GCE or OpenStack load-balancers)\n+optional"}}}, "v1ManagedFieldsEntry": {"description": "ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource\nthat the fieldset applies to.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the version of this resource that this field set\napplies to. The format is \"group/version\" just like the top-level\nAPIVersion field. It is necessary to track the version of a field\nset because it cannot be automatically converted.", "type": "string"}, "fields": {"$ref": "#/definitions/v1Fields"}, "manager": {"description": "Manager is an identifier of the workflow managing these fields.", "type": "string"}, "operation": {"description": "Operation is the type of operation which lead to this ManagedFieldsEntry being created.\nThe only valid values for this field are 'Apply' and 'Update'.", "type": "string"}, "time": {"$ref": "#/definitions/v1Time"}}}, "v1MicroTime": {"description": "MicroTime is version of Time with microsecond level precision.\n\n+protobuf.options.marshal=false\n+protobuf.as=Timestamp\n+protobuf.options.(gogoproto.goproto_stringer)=false", "type": "object", "properties": {"nanos": {"description": "Non-negative fractions of a second at nanosecond resolution. Negative\nsecond values with fractions must still have non-negative nanos values\nthat count forward in time. Must be from 0 to 999,999,999\ninclusive. This field may be limited in precision depending on context.", "type": "integer", "format": "int32"}, "seconds": {"description": "Represents seconds of UTC time since Unix epoch\n1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to\n9999-12-31T23:59:59Z inclusive.", "type": "string", "format": "int64"}}}, "v1ObjectMeta": {"description": "ObjectMeta is metadata that all persisted resources must have, which includes all objects\nusers must create.", "type": "object", "properties": {"annotations": {"type": "object", "title": "Annotations is an unstructured key value map stored with a resource that may be\nset by external tools to store and retrieve arbitrary metadata. They are not\nqueryable and should be preserved when modifying objects.\nMore info: http://kubernetes.io/docs/user-guide/annotations\n+optional", "additionalProperties": {"type": "string"}}, "clusterName": {"type": "string", "title": "The name of the cluster which the object belongs to.\nThis is used to distinguish resources with same name and namespace in different clusters.\nThis field is not set anywhere right now and apiserver is going to ignore it if set in create or update request.\n+optional"}, "creationTimestamp": {"$ref": "#/definitions/v1Time"}, "deletionGracePeriodSeconds": {"type": "string", "format": "int64", "title": "Number of seconds allowed for this object to gracefully terminate before\nit will be removed from the system. Only set when deletionTimestamp is also set.\nMay only be shortened.\nRead-only.\n+optional"}, "deletionTimestamp": {"$ref": "#/definitions/v1Time"}, "finalizers": {"type": "array", "title": "Must be empty before the object is deleted from the registry. Each entry\nis an identifier for the responsible component that will remove the entry\nfrom the list. If the deletionTimestamp of the object is non-nil, entries\nin this list can only be removed.\n+optional\n+patchStrategy=merge", "items": {"type": "string"}}, "generateName": {"description": "GenerateName is an optional prefix, used by the server, to generate a unique\nname ONLY IF the Name field has not been provided.\nIf this field is used, the name returned to the client will be different\nthan the name passed. This value will also be combined with a unique suffix.\nThe provided value has the same validation rules as the Name field,\nand may be truncated by the length of the suffix required to make the value\nunique on the server.\n\nIf this field is specified and the generated name exists, the server will\nNOT return a 409 - instead, it will either return 201 Created or 500 with Reason\nServerTimeout indicating a unique name could not be found in the time allotted, and the client\nshould retry (optionally after the time indicated in the Retry-After header).\n\nApplied only if Name is not specified.\nMore info: https://git.k8s.io/community/contributors/devel/api-conventions.md#idempotency\n+optional", "type": "string"}, "generation": {"type": "string", "format": "int64", "title": "A sequence number representing a specific generation of the desired state.\nPopulated by the system. Read-only.\n+optional"}, "initializers": {"$ref": "#/definitions/v1Initializers"}, "labels": {"type": "object", "title": "Map of string keys and values that can be used to organize and categorize\n(scope and select) objects. May match selectors of replication controllers\nand services.\nMore info: http://kubernetes.io/docs/user-guide/labels\n+optional", "additionalProperties": {"type": "string"}}, "managedFields": {"description": "ManagedFields maps workflow-id and version to the set of fields\nthat are managed by that workflow. This is mostly for internal\nhousekeeping, and users typically shouldn't need to set or\nunderstand this field. A workflow can be the user's name, a\ncontroller's name, or the name of a specific apply path like\n\"ci-cd\". The set of fields is always in the version that the\nworkflow used when modifying the object.\n\nThis field is alpha and can be changed or removed without notice.\n\n+optional", "type": "array", "items": {"$ref": "#/definitions/v1ManagedFieldsEntry"}}, "name": {"type": "string", "title": "Name must be unique within a namespace. Is required when creating resources, although\nsome resources may allow a client to request the generation of an appropriate name\nautomatically. Name is primarily intended for creation idempotence and configuration\ndefinition.\nCannot be updated.\nMore info: http://kubernetes.io/docs/user-guide/identifiers#names\n+optional"}, "namespace": {"description": "Namespace defines the space within each name must be unique. An empty namespace is\nequivalent to the \"default\" namespace, but \"default\" is the canonical representation.\nNot all objects are required to be scoped to a namespace - the value of this field for\nthose objects will be empty.\n\nMust be a DNS_LABEL.\nCannot be updated.\nMore info: http://kubernetes.io/docs/user-guide/namespaces\n+optional", "type": "string"}, "ownerReferences": {"type": "array", "title": "List of objects depended by this object. If ALL objects in the list have\nbeen deleted, this object will be garbage collected. If this object is managed by a controller,\nthen an entry in this list will point to this controller, with the controller field set to true.\nThere cannot be more than one managing controller.\n+optional\n+patchMergeKey=uid\n+patchStrategy=merge", "items": {"$ref": "#/definitions/v1OwnerReference"}}, "resourceVersion": {"description": "An opaque value that represents the internal version of this object that can\nbe used by clients to determine when objects have changed. May be used for optimistic\nconcurrency, change detection, and the watch operation on a resource or set of resources.\nClients must treat these values as opaque and passed unmodified back to the server.\nThey may only be valid for a particular resource or set of resources.\n\nPopulated by the system.\nRead-only.\nValue must be treated as opaque by clients and .\nMore info: https://git.k8s.io/community/contributors/devel/api-conventions.md#concurrency-control-and-consistency\n+optional", "type": "string"}, "selfLink": {"type": "string", "title": "SelfLink is a URL representing this object.\nPopulated by the system.\nRead-only.\n+optional"}, "uid": {"description": "UID is the unique in time and space value for this object. It is typically generated by\nthe server on successful creation of a resource and is not allowed to change on PUT\noperations.\n\nPopulated by the system.\nRead-only.\nMore info: http://kubernetes.io/docs/user-guide/identifiers#uids\n+optional", "type": "string"}}}, "v1ObjectReference": {"type": "object", "title": "ObjectReference contains enough information to let you inspect or modify the referred object.\n+k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object", "properties": {"apiVersion": {"type": "string", "title": "API version of the referent.\n+optional"}, "fieldPath": {"type": "string", "title": "If referring to a piece of an object instead of an entire object, this string\nshould contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].\nFor example, if the object reference is to a container within a pod, this would take on a value like:\n\"spec.containers{name}\" (where \"name\" refers to the name of the container that triggered\nthe event) or if no container name is specified \"spec.containers[2]\" (container with\nindex 2 in this pod). This syntax is chosen only to have some well-defined way of\nreferencing a part of an object.\nTODO: this design is not final and this field is subject to change in the future.\n+optional"}, "kind": {"type": "string", "title": "Kind of the referent.\nMore info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds\n+optional"}, "name": {"type": "string", "title": "Name of the referent.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\n+optional"}, "namespace": {"type": "string", "title": "Namespace of the referent.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/\n+optional"}, "resourceVersion": {"type": "string", "title": "Specific resourceVersion to which this reference is made, if any.\nMore info: https://git.k8s.io/community/contributors/devel/api-conventions.md#concurrency-control-and-consistency\n+optional"}, "uid": {"type": "string", "title": "UID of the referent.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids\n+optional"}}}, "v1OwnerReference": {"description": "OwnerReference contains enough information to let you identify an owning\nobject. An owning object must be in the same namespace as the dependent, or\nbe cluster-scoped, so there is no namespace field.", "type": "object", "properties": {"apiVersion": {"description": "API version of the referent.", "type": "string"}, "blockOwnerDeletion": {"type": "boolean", "format": "boolean", "title": "If true, AND if the owner has the \"foregroundDeletion\" finalizer, then\nthe owner cannot be deleted from the key-value store until this\nreference is removed.\nDefaults to false.\nTo set this field, a user needs \"delete\" permission of the owner,\notherwise 422 (Unprocessable Entity) will be returned.\n+optional"}, "controller": {"type": "boolean", "format": "boolean", "title": "If true, this reference points to the managing controller.\n+optional"}, "kind": {"type": "string", "title": "Kind of the referent.\nMore info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds"}, "name": {"type": "string", "title": "Name of the referent.\nMore info: http://kubernetes.io/docs/user-guide/identifiers#names"}, "uid": {"type": "string", "title": "UID of the referent.\nMore info: http://kubernetes.io/docs/user-guide/identifiers#uids"}}}, "v1Status": {"description": "Status is a return value for calls that don't return other objects.", "type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "Suggested HTTP return code for this status, 0 if not set.\n+optional"}, "details": {"$ref": "#/definitions/v1StatusDetails"}, "message": {"type": "string", "title": "A human-readable description of the status of this operation.\n+optional"}, "metadata": {"$ref": "#/definitions/v1ListMeta"}, "reason": {"type": "string", "title": "A machine-readable description of why this operation is in the\n\"Failure\" status. If this value is empty there\nis no information available. A Reason clarifies an HTTP status\ncode but does not override it.\n+optional"}, "status": {"type": "string", "title": "Status of the operation.\nOne of: \"Success\" or \"Failure\".\nMore info: https://git.k8s.io/community/contributors/devel/api-conventions.md#spec-and-status\n+optional"}}}, "v1StatusCause": {"description": "StatusCause provides more information about an api.Status failure, including\ncases when multiple errors are encountered.", "type": "object", "properties": {"field": {"description": "The field of the resource that has caused this error, as named by its JSON\nserialization. May include dot and postfix notation for nested attributes.\nArrays are zero-indexed.  Fields may appear more than once in an array of\ncauses due to fields having multiple errors.\nOptional.\n\nExamples:\n  \"name\" - the field \"name\" on the current resource\n  \"items[0].name\" - the field \"name\" on the first array entry in \"items\"\n+optional", "type": "string"}, "message": {"type": "string", "title": "A human-readable description of the cause of the error.  This field may be\npresented as-is to a reader.\n+optional"}, "reason": {"type": "string", "title": "A machine-readable description of the cause of the error. If this value is\nempty there is no information available.\n+optional"}}}, "v1StatusDetails": {"description": "StatusDetails is a set of additional properties that MAY be set by the\nserver to provide additional information about a response. The Reason\nfield of a Status object defines what attributes will be set. Clients\nmust ignore fields that do not match the defined type of each attribute,\nand should assume that any attribute may be empty, invalid, or under\ndefined.", "type": "object", "properties": {"causes": {"type": "array", "title": "The Causes array includes more details associated with the StatusReason\nfailure. Not all StatusReasons may provide detailed causes.\n+optional", "items": {"$ref": "#/definitions/v1StatusCause"}}, "group": {"type": "string", "title": "The group attribute of the resource associated with the status StatusReason.\n+optional"}, "kind": {"type": "string", "title": "The kind attribute of the resource associated with the status StatusReason.\nOn some operations may differ from the requested resource Kind.\nMore info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds\n+optional"}, "name": {"type": "string", "title": "The name attribute of the resource associated with the status StatusReason\n(when there is a single name which can be described).\n+optional"}, "retryAfterSeconds": {"type": "integer", "format": "int32", "title": "If specified, the time in seconds before the operation should be retried. Some errors may indicate\nthe client must take an alternate action - for those errors this field may indicate how long to wait\nbefore taking the alternate action.\n+optional"}, "uid": {"type": "string", "title": "UID of the resource.\n(when there is a single resource which can be described).\nMore info: http://kubernetes.io/docs/user-guide/identifiers#uids\n+optional"}}}, "v1Time": {"description": "Time is a wrapper around time.Time which supports correct\nmarshaling to YAML and JSON.  Wrappers are provided for many\nof the factory methods that the time package offers.\n\n+protobuf.options.marshal=false\n+protobuf.as=Timestamp\n+protobuf.options.(gogoproto.goproto_stringer)=false", "type": "object", "properties": {"nanos": {"description": "Non-negative fractions of a second at nanosecond resolution. Negative\nsecond values with fractions must still have non-negative nanos values\nthat count forward in time. Must be from 0 to 999,999,999\ninclusive. This field may be limited in precision depending on context.", "type": "integer", "format": "int32"}, "seconds": {"description": "Represents seconds of UTC time since Unix epoch\n1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to\n9999-12-31T23:59:59Z inclusive.", "type": "string", "format": "int64"}}}, "v1alpha1AWSAuthConfig": {"type": "object", "title": "AWSAuthConfig is an AWS IAM authentication configuration", "properties": {"clusterName": {"type": "string", "title": "ClusterName contains AWS cluster name"}, "roleARN": {"description": "RoleARN contains optional role ARN. If set then AWS IAM Authenticator assume a role to perform cluster operations instead of the default AWS credential provider chain.", "type": "string"}}}, "v1alpha1AppProject": {"type": "object", "title": "AppProject provides a logical grouping of applications, providing controls for:\n* where the apps may deploy to (cluster whitelist)\n* what may be deployed (repository whitelist, resource whitelist/blacklist)\n* who can access these applications (roles, OIDC group claims bindings)\n* and what they can do (RBAC policies)\n* automation access to these roles (JWT tokens)\n+genclient\n+genclient:noStatus\n+k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object\n+kubebuilder:resource:path=appprojects,shortName=appproj;appprojs", "properties": {"metadata": {"$ref": "#/definitions/v1ObjectMeta"}, "spec": {"$ref": "#/definitions/v1alpha1AppProjectSpec"}}}, "v1alpha1AppProjectList": {"type": "object", "title": "AppProjectList is list of AppProject resources\n+k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1AppProject"}}, "metadata": {"$ref": "#/definitions/v1ListMeta"}}}, "v1alpha1AppProjectSpec": {"type": "object", "title": "AppProjectSpec is the specification of an AppProject", "properties": {"clusterResourceWhitelist": {"type": "array", "title": "ClusterResourceWhitelist contains list of whitelisted cluster level resources", "items": {"$ref": "#/definitions/v1GroupKind"}}, "description": {"type": "string", "title": "Description contains optional project description"}, "destinations": {"type": "array", "title": "Destinations contains list of destinations available for deployment", "items": {"$ref": "#/definitions/v1alpha1ApplicationDestination"}}, "namespaceResourceBlacklist": {"type": "array", "title": "NamespaceResourceBlacklist contains list of blacklisted namespace level resources", "items": {"$ref": "#/definitions/v1GroupKind"}}, "roles": {"type": "array", "title": "Roles are user defined RBAC roles associated with this project", "items": {"$ref": "#/definitions/v1alpha1ProjectRole"}}, "sourceRepos": {"type": "array", "title": "SourceRepos contains list of git repository URLs which can be used for deployment", "items": {"type": "string"}}}}, "v1alpha1Application": {"type": "object", "title": "Application is a definition of Application resource.\n+genclient\n+genclient:noStatus\n+k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object\n+kubebuilder:resource:path=applications,shortName=app;apps", "properties": {"metadata": {"$ref": "#/definitions/v1ObjectMeta"}, "operation": {"$ref": "#/definitions/v1alpha1Operation"}, "spec": {"$ref": "#/definitions/v1alpha1ApplicationSpec"}, "status": {"$ref": "#/definitions/v1alpha1ApplicationStatus"}}}, "v1alpha1ApplicationCondition": {"type": "object", "title": "ApplicationCondition contains details about current application condition", "properties": {"message": {"type": "string", "title": "Message contains human-readable message indicating details about condition"}, "type": {"type": "string", "title": "Type is an application condition type"}}}, "v1alpha1ApplicationDestination": {"type": "object", "title": "ApplicationDestination contains deployment destination information", "properties": {"namespace": {"type": "string", "title": "Namespace overrides the environment namespace value in the ksonnet app.yaml"}, "server": {"type": "string", "title": "Server overrides the environment server value in the ksonnet app.yaml"}}}, "v1alpha1ApplicationList": {"type": "object", "title": "ApplicationList is list of Application resources\n+k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1Application"}}, "metadata": {"$ref": "#/definitions/v1ListMeta"}}}, "v1alpha1ApplicationSource": {"description": "ApplicationSource contains information about github repository, path within repository and target application environment.", "type": "object", "properties": {"directory": {"$ref": "#/definitions/v1alpha1ApplicationSourceDirectory"}, "helm": {"$ref": "#/definitions/v1alpha1ApplicationSourceHelm"}, "ksonnet": {"$ref": "#/definitions/v1alpha1ApplicationSourceKsonnet"}, "kustomize": {"$ref": "#/definitions/v1alpha1ApplicationSourceKustomize"}, "path": {"type": "string", "title": "Path is a directory path within the repository containing a"}, "plugin": {"$ref": "#/definitions/v1alpha1ApplicationSourcePlugin"}, "repoURL": {"type": "string", "title": "RepoURL is the git repository URL of the application manifests"}, "targetRevision": {"type": "string", "title": "TargetRevision defines the commit, tag, or branch in which to sync the application to.\nIf omitted, will sync to HEAD"}}}, "v1alpha1ApplicationSourceDirectory": {"type": "object", "properties": {"jsonnet": {"$ref": "#/definitions/v1alpha1ApplicationSourceJsonnet"}, "recurse": {"type": "boolean", "format": "boolean"}}}, "v1alpha1ApplicationSourceHelm": {"type": "object", "title": "ApplicationSourceHelm holds helm specific options", "properties": {"parameters": {"type": "array", "title": "Parameters are parameters to the helm template", "items": {"$ref": "#/definitions/v1alpha1HelmParameter"}}, "releaseName": {"type": "string", "title": "The Helm release name. If omitted it will use the application name"}, "valueFiles": {"type": "array", "title": "ValuesFiles is a list of Helm value files to use when generating a template", "items": {"type": "string"}}}}, "v1alpha1ApplicationSourceJsonnet": {"type": "object", "title": "ApplicationSourceJsonnet holds jsonnet specific options", "properties": {"extVars": {"type": "array", "title": "ExtVars is a list of Jsonnet External Variables", "items": {"$ref": "#/definitions/v1alpha1JsonnetVar"}}, "tlas": {"type": "array", "title": "TLAS is a list of Jsonnet Top-level Arguments", "items": {"$ref": "#/definitions/v1alpha1JsonnetVar"}}}}, "v1alpha1ApplicationSourceKsonnet": {"type": "object", "title": "ApplicationSourceKsonnet holds ksonnet specific options", "properties": {"environment": {"type": "string", "title": "Environment is a ksonnet application environment name"}, "parameters": {"type": "array", "title": "Parameters are a list of ksonnet component parameter override values", "items": {"$ref": "#/definitions/v1alpha1KsonnetParameter"}}}}, "v1alpha1ApplicationSourceKustomize": {"type": "object", "title": "ApplicationSourceKustomize holds kustomize specific options", "properties": {"commonLabels": {"type": "object", "title": "CommonLabels adds additional kustomize commonLabels", "additionalProperties": {"type": "string"}}, "images": {"type": "array", "title": "Images are kustomize image overrides", "items": {"type": "string"}}, "namePrefix": {"type": "string", "title": "NamePrefix is a prefix appended to resources for kustomize apps"}}}, "v1alpha1ApplicationSourcePlugin": {"type": "object", "title": "ApplicationSourcePlugin holds config management plugin specific options", "properties": {"env": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1EnvEntry"}}, "name": {"type": "string"}}}, "v1alpha1ApplicationSpec": {"description": "ApplicationSpec represents desired application state. Contains link to repository with application definition and additional parameters link definition revision.", "type": "object", "properties": {"destination": {"$ref": "#/definitions/v1alpha1ApplicationDestination"}, "ignoreDifferences": {"type": "array", "title": "IgnoreDifferences controls resources fields which should be ignored during comparison", "items": {"$ref": "#/definitions/v1alpha1ResourceIgnoreDifferences"}}, "info": {"type": "array", "title": "Infos contains a list of useful information (URLs, email addresses, and plain text) that relates to the application", "items": {"$ref": "#/definitions/v1alpha1Info"}}, "project": {"description": "Project is a application project name. Empty name means that application belongs to 'default' project.", "type": "string"}, "source": {"$ref": "#/definitions/v1alpha1ApplicationSource"}, "syncPolicy": {"$ref": "#/definitions/v1alpha1SyncPolicy"}}}, "v1alpha1ApplicationStatus": {"type": "object", "title": "ApplicationStatus contains information about application sync, health status", "properties": {"conditions": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1ApplicationCondition"}}, "health": {"$ref": "#/definitions/v1alpha1HealthStatus"}, "history": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1RevisionHistory"}}, "observedAt": {"$ref": "#/definitions/v1Time"}, "operationState": {"$ref": "#/definitions/v1alpha1OperationState"}, "reconciledAt": {"$ref": "#/definitions/v1Time"}, "resources": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1ResourceStatus"}}, "sourceType": {"type": "string"}, "summary": {"$ref": "#/definitions/v1alpha1ApplicationSummary"}, "sync": {"$ref": "#/definitions/v1alpha1SyncStatus"}}}, "v1alpha1ApplicationSummary": {"type": "object", "properties": {"externalURLs": {"description": "ExternalURLs holds all external URLs of application child resources.", "type": "array", "items": {"type": "string"}}, "images": {"description": "Images holds all images of application child resources.", "type": "array", "items": {"type": "string"}}}}, "v1alpha1ApplicationTree": {"type": "object", "title": "ApplicationTree holds nodes which belongs to the application", "properties": {"nodes": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1ResourceNode"}}}}, "v1alpha1ApplicationWatchEvent": {"description": "ApplicationWatchEvent contains information about application change.", "type": "object", "properties": {"application": {"$ref": "#/definitions/v1alpha1Application"}, "type": {"type": "string"}}}, "v1alpha1Cluster": {"type": "object", "title": "Cluster is the definition of a cluster resource", "properties": {"config": {"$ref": "#/definitions/v1alpha1ClusterConfig"}, "connectionState": {"$ref": "#/definitions/v1alpha1ConnectionState"}, "name": {"type": "string", "title": "Name of the cluster. If omitted, will use the server address"}, "server": {"type": "string", "title": "Server is the API server URL of the Kubernetes cluster"}}}, "v1alpha1ClusterConfig": {"description": "ClusterConfig is the configuration attributes. This structure is subset of the go-client\nrest.Config with annotations added for marshalling.", "type": "object", "properties": {"awsAuthConfig": {"$ref": "#/definitions/v1alpha1AWSAuthConfig"}, "bearerToken": {"description": "Server requires Bearer authentication. This client will not attempt to use\nrefresh tokens for an OAuth2 flow.\nTODO: demonstrate an OAuth2 compatible client.", "type": "string"}, "password": {"type": "string"}, "tlsClientConfig": {"$ref": "#/definitions/v1alpha1TLSClientConfig"}, "username": {"type": "string", "title": "Server requires Basic authentication"}}}, "v1alpha1ClusterList": {"description": "ClusterList is a collection of Clusters.", "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1Cluster"}}, "metadata": {"$ref": "#/definitions/v1ListMeta"}}}, "v1alpha1ComparedTo": {"type": "object", "title": "ComparedTo contains application source and target which was used for resources comparison", "properties": {"destination": {"$ref": "#/definitions/v1alpha1ApplicationDestination"}, "source": {"$ref": "#/definitions/v1alpha1ApplicationSource"}}}, "v1alpha1ConnectionState": {"type": "object", "title": "ConnectionState contains information about remote resource connection state", "properties": {"attemptedAt": {"$ref": "#/definitions/v1Time"}, "message": {"type": "string"}, "status": {"type": "string"}}}, "v1alpha1EnvEntry": {"type": "object", "properties": {"name": {"type": "string", "title": "the name, usually uppercase"}, "value": {"type": "string", "title": "the value"}}}, "v1alpha1HealthStatus": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}}}, "v1alpha1HelmParameter": {"type": "object", "title": "HelmParameter is a parameter to a helm template", "properties": {"forceString": {"type": "boolean", "format": "boolean", "title": "ForceString determines whether to tell <PERSON><PERSON> to interpret booleans and numbers as strings"}, "name": {"type": "string", "title": "Name is the name of the helm parameter"}, "value": {"type": "string", "title": "Value is the value for the helm parameter"}}}, "v1alpha1Info": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}, "v1alpha1InfoItem": {"type": "object", "title": "InfoItem contains human readable information about object", "properties": {"name": {"description": "Name is a human readable title for this piece of information.", "type": "string"}, "value": {"description": "Value is human readable content.", "type": "string"}}}, "v1alpha1JWTToken": {"type": "object", "title": "JWTToken holds the issuedAt and expiresAt values of a token", "properties": {"exp": {"type": "string", "format": "int64"}, "iat": {"type": "string", "format": "int64"}}}, "v1alpha1JsonnetVar": {"type": "object", "title": "JsonnetVar is a jsonnet variable", "properties": {"code": {"type": "boolean", "format": "boolean"}, "name": {"type": "string"}, "value": {"type": "string"}}}, "v1alpha1KsonnetParameter": {"type": "object", "title": "KsonnetParameter is a ksonnet component parameter", "properties": {"component": {"type": "string"}, "name": {"type": "string"}, "value": {"type": "string"}}}, "v1alpha1KustomizeOptions": {"type": "object", "title": "KustomizeOptions are options for kustomize to use when building manifests", "properties": {"buildOptions": {"type": "string", "title": "BuildOptions is a string of build parameters to use when calling `kustomize build`"}}}, "v1alpha1Operation": {"description": "Operation contains requested operation parameters.", "type": "object", "properties": {"sync": {"$ref": "#/definitions/v1alpha1SyncOperation"}}}, "v1alpha1OperationState": {"description": "OperationState contains information about state of currently performing operation on application.", "type": "object", "properties": {"finishedAt": {"$ref": "#/definitions/v1Time"}, "message": {"description": "Message hold any pertinent messages when attempting to perform operation (typically errors).", "type": "string"}, "operation": {"$ref": "#/definitions/v1alpha1Operation"}, "phase": {"type": "string", "title": "Phase is the current phase of the operation"}, "startedAt": {"$ref": "#/definitions/v1Time"}, "syncResult": {"$ref": "#/definitions/v1alpha1SyncOperationResult"}}}, "v1alpha1ProjectRole": {"type": "object", "title": "ProjectRole represents a role that has access to a project", "properties": {"description": {"type": "string", "title": "Description is a description of the role"}, "groups": {"type": "array", "title": "Groups are a list of OIDC group claims bound to this role", "items": {"type": "string"}}, "jwtTokens": {"type": "array", "title": "JWTTokens are a list of generated JWT tokens bound to this role", "items": {"$ref": "#/definitions/v1alpha1JWTToken"}}, "name": {"type": "string", "title": "Name is a name for this role"}, "policies": {"type": "array", "title": "Policies Stores a list of casbin formated strings that define access policies for the role in the project", "items": {"type": "string"}}}}, "v1alpha1Repository": {"type": "object", "title": "Repository is a Git repository holding application configurations", "properties": {"connectionState": {"$ref": "#/definitions/v1alpha1ConnectionState"}, "enableLfs": {"type": "boolean", "format": "boolean", "title": "Whether git-lfs support should be enabled for this repo"}, "insecure": {"type": "boolean", "format": "boolean", "title": "Whether the repo is insecure"}, "insecureIgnoreHostKey": {"type": "boolean", "format": "boolean", "title": "InsecureIgnoreHostKey should not be used anymore, Insecure is favoured"}, "password": {"type": "string", "title": "Password for authenticating at the repo server"}, "repo": {"type": "string", "title": "URL of the repo"}, "sshPrivateKey": {"type": "string", "title": "SSH private key data for authenticating at the repo server"}, "tlsClientCertData": {"type": "string", "title": "TLS client cert data for authenticating at the repo server"}, "tlsClientCertKey": {"type": "string", "title": "TLS client cert key for authenticating at the repo server"}, "username": {"type": "string", "title": "Username for authenticating at the repo server"}}}, "v1alpha1RepositoryCertificate": {"type": "object", "title": "A RepositoryCertificate is either SSH known hosts entry or TLS certificate", "properties": {"certData": {"type": "string", "format": "byte", "title": "Actual certificate data, protocol dependent"}, "certInfo": {"type": "string", "title": "Additional certificate info (e.g. SSH fingerprint, X509 CommonName)"}, "certSubType": {"type": "string", "title": "The sub type of the cert, i.e. \"ssh-rsa\""}, "certType": {"type": "string", "title": "Type of certificate - currently \"https\" or \"ssh\""}, "serverName": {"type": "string", "title": "Name of the server the certificate is intended for"}}}, "v1alpha1RepositoryCertificateList": {"type": "object", "title": "RepositoryCertificateList is a collection of RepositoryCertificates", "properties": {"items": {"type": "array", "title": "List of certificates to be processed", "items": {"$ref": "#/definitions/v1alpha1RepositoryCertificate"}}, "metadata": {"$ref": "#/definitions/v1ListMeta"}}}, "v1alpha1RepositoryList": {"description": "RepositoryList is a collection of Repositories.", "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1Repository"}}, "metadata": {"$ref": "#/definitions/v1ListMeta"}}}, "v1alpha1ResourceAction": {"type": "object", "properties": {"name": {"type": "string"}, "params": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1ResourceActionParam"}}}}, "v1alpha1ResourceActionParam": {"type": "object", "properties": {"default": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "v1alpha1ResourceDiff": {"type": "object", "title": "ResourceDiff holds the diff of a live and target resource object", "properties": {"diff": {"type": "string"}, "group": {"type": "string"}, "hook": {"type": "boolean", "format": "boolean"}, "kind": {"type": "string"}, "liveState": {"type": "string"}, "name": {"type": "string"}, "namespace": {"type": "string"}, "targetState": {"type": "string"}}}, "v1alpha1ResourceIgnoreDifferences": {"description": "ResourceIgnoreDifferences contains resource filter and list of json paths which should be ignored during comparison with live state.", "type": "object", "properties": {"group": {"type": "string"}, "jsonPointers": {"type": "array", "items": {"type": "string"}}, "kind": {"type": "string"}, "name": {"type": "string"}, "namespace": {"type": "string"}}}, "v1alpha1ResourceNetworkingInfo": {"type": "object", "title": "ResourceNetworkingInfo holds networking resource related information", "properties": {"externalURLs": {"description": "ExternalURLs holds list of URLs which should be available externally. List is populated for ingress resources using rules hostnames.", "type": "array", "items": {"type": "string"}}, "ingress": {"type": "array", "items": {"$ref": "#/definitions/v1LoadBalancerIngress"}}, "labels": {"type": "object", "additionalProperties": {"type": "string"}}, "targetLabels": {"type": "object", "additionalProperties": {"type": "string"}}, "targetRefs": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1ResourceRef"}}}}, "v1alpha1ResourceNode": {"type": "object", "title": "ResourceNode contains information about live resource and its children", "properties": {"health": {"$ref": "#/definitions/v1alpha1HealthStatus"}, "images": {"type": "array", "items": {"type": "string"}}, "info": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1InfoItem"}}, "networkingInfo": {"$ref": "#/definitions/v1alpha1ResourceNetworkingInfo"}, "parentRefs": {"type": "array", "items": {"$ref": "#/definitions/v1alpha1ResourceRef"}}, "resourceRef": {"$ref": "#/definitions/v1alpha1ResourceRef"}, "resourceVersion": {"type": "string"}}}, "v1alpha1ResourceOverride": {"type": "object", "title": "ResourceOverride holds configuration to customize resource diffing and health assessment", "properties": {"actions": {"type": "string"}, "healthLua": {"type": "string"}, "ignoreDifferences": {"type": "string"}}}, "v1alpha1ResourceRef": {"type": "object", "title": "ResourceRef includes fields which unique identify resource", "properties": {"group": {"type": "string"}, "kind": {"type": "string"}, "name": {"type": "string"}, "namespace": {"type": "string"}, "uid": {"type": "string"}, "version": {"type": "string"}}}, "v1alpha1ResourceResult": {"type": "object", "title": "ResourceResult holds the operation result details of a specific resource", "properties": {"group": {"type": "string"}, "hookPhase": {"type": "string", "title": "the state of any operation associated with this resource OR hook\nnote: can contain values for non-hook resources"}, "hookType": {"type": "string", "title": "the type of the hook, empty for non-hook resources"}, "kind": {"type": "string"}, "message": {"type": "string", "title": "message for the last sync OR operation"}, "name": {"type": "string"}, "namespace": {"type": "string"}, "status": {"type": "string", "title": "the final result of the sync, this is be empty if the resources is yet to be applied/pruned and is always zero-value for hooks"}, "syncPhase": {"type": "string", "title": "indicates the particular phase of the sync that this is for"}, "version": {"type": "string"}}}, "v1alpha1ResourceStatus": {"type": "object", "title": "ResourceStatus holds the current sync and health status of a resource", "properties": {"group": {"type": "string"}, "health": {"$ref": "#/definitions/v1alpha1HealthStatus"}, "hook": {"type": "boolean", "format": "boolean"}, "kind": {"type": "string"}, "name": {"type": "string"}, "namespace": {"type": "string"}, "requiresPruning": {"type": "boolean", "format": "boolean"}, "status": {"type": "string"}, "version": {"type": "string"}}}, "v1alpha1RevisionHistory": {"type": "object", "title": "RevisionHistory contains information relevant to an application deployment", "properties": {"deployedAt": {"$ref": "#/definitions/v1Time"}, "id": {"type": "string", "format": "int64"}, "revision": {"type": "string"}, "source": {"$ref": "#/definitions/v1alpha1ApplicationSource"}}}, "v1alpha1RevisionMetadata": {"type": "object", "title": "data about a specific revision within a repo", "properties": {"author": {"type": "string", "title": "who authored this revision,\ntypically their name and email, e.g. \"<PERSON> <<EMAIL>>\",\nbut might not match this example"}, "date": {"$ref": "#/definitions/v1Time"}, "message": {"type": "string", "title": "the message associated with the revision,\nprobably the commit message,\nthis is truncated to the first newline or 64 characters (which ever comes first)"}, "tags": {"type": "array", "title": "tags on the revision,\nnote - tags can move from one revision to another", "items": {"type": "string"}}}}, "v1alpha1SyncOperation": {"description": "SyncOperation contains sync operation details.", "type": "object", "properties": {"dryRun": {"type": "boolean", "format": "boolean", "title": "DryRun will perform a `kubectl apply --dry-run` without actually performing the sync"}, "manifests": {"type": "array", "title": "Manifests is an optional field that overrides sync source with a local directory for development", "items": {"type": "string"}}, "prune": {"type": "boolean", "format": "boolean", "title": "Prune deletes resources that are no longer tracked in git"}, "resources": {"type": "array", "title": "Resources describes which resources to sync", "items": {"$ref": "#/definitions/v1alpha1SyncOperationResource"}}, "revision": {"description": "Revision is the git revision in which to sync the application to.\nIf omitted, will use the revision specified in app spec.", "type": "string"}, "source": {"$ref": "#/definitions/v1alpha1ApplicationSource"}, "syncStrategy": {"$ref": "#/definitions/v1alpha1SyncStrategy"}}}, "v1alpha1SyncOperationResource": {"description": "SyncOperationResource contains resources to sync.", "type": "object", "properties": {"group": {"type": "string"}, "kind": {"type": "string"}, "name": {"type": "string"}}}, "v1alpha1SyncOperationResult": {"type": "object", "title": "SyncOperationResult represent result of sync operation", "properties": {"resources": {"type": "array", "title": "Resources holds the sync result of each individual resource", "items": {"$ref": "#/definitions/v1alpha1ResourceResult"}}, "revision": {"type": "string", "title": "Revision holds the git commit SHA of the sync"}, "source": {"$ref": "#/definitions/v1alpha1ApplicationSource"}}}, "v1alpha1SyncPolicy": {"type": "object", "title": "SyncPolicy controls when a sync will be performed in response to updates in git", "properties": {"automated": {"$ref": "#/definitions/v1alpha1SyncPolicyAutomated"}}}, "v1alpha1SyncPolicyAutomated": {"type": "object", "title": "SyncPolicyAutomated controls the behavior of an automated sync", "properties": {"prune": {"type": "boolean", "format": "boolean", "title": "Prune will prune resources automatically as part of automated sync (default: false)"}, "selfHeal": {"type": "boolean", "format": "boolean", "title": "SelfHeal enables auto-syncing if  (default: false)"}}}, "v1alpha1SyncStatus": {"description": "SyncStatus is a comparison result of application spec and deployed application.", "type": "object", "properties": {"comparedTo": {"$ref": "#/definitions/v1alpha1ComparedTo"}, "revision": {"type": "string"}, "status": {"type": "string"}}}, "v1alpha1SyncStrategy": {"type": "object", "title": "SyncStrategy controls the manner in which a sync is performed", "properties": {"apply": {"$ref": "#/definitions/v1alpha1SyncStrategyApply"}, "hook": {"$ref": "#/definitions/v1alpha1SyncStrategyHook"}}}, "v1alpha1SyncStrategyApply": {"type": "object", "title": "SyncStrategyApply uses `kubectl apply` to perform the apply", "properties": {"force": {"description": "Force indicates whether or not to supply the --force flag to `kubectl apply`.\nThe --force flag deletes and re-create the resource, when PATCH encounters conflict and has\nretried for 5 times.", "type": "boolean", "format": "boolean"}}}, "v1alpha1SyncStrategyHook": {"description": "SyncStrategyHook will perform a sync using hooks annotations.\nIf no hook annotation is specified falls back to `kubectl apply`.", "type": "object", "properties": {"syncStrategyApply": {"$ref": "#/definitions/v1alpha1SyncStrategyApply"}}}, "v1alpha1TLSClientConfig": {"type": "object", "title": "TLSClientConfig contains settings to enable transport layer security", "properties": {"caData": {"type": "string", "format": "byte", "title": "CAData holds PEM-encoded bytes (typically read from a root certificates bundle).\nCAData takes precedence over CAFile"}, "certData": {"type": "string", "format": "byte", "title": "CertData holds PEM-encoded bytes (typically read from a client certificate file).\nCertData takes precedence over CertFile"}, "insecure": {"description": "Server should be accessed without verifying the TLS certificate. For testing only.", "type": "boolean", "format": "boolean"}, "keyData": {"type": "string", "format": "byte", "title": "KeyData holds PEM-encoded bytes (typically read from a client certificate key file).\nKeyData takes precedence over KeyFile"}, "serverName": {"description": "ServerName is passed to the server for SNI and is used in the client to check server\ncertificates against. If ServerName is empty, the hostname used to contact the\nserver is used.", "type": "string"}}}, "versionVersionMessage": {"type": "object", "title": "VersionMessage represents version of the Argo CD API server", "properties": {"BuildDate": {"type": "string"}, "Compiler": {"type": "string"}, "GitCommit": {"type": "string"}, "GitTag": {"type": "string"}, "GitTreeState": {"type": "string"}, "GoVersion": {"type": "string"}, "KsonnetVersion": {"type": "string"}, "Platform": {"type": "string"}, "Version": {"type": "string"}}}}}